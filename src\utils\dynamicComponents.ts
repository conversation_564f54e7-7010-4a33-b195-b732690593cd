import {
  UniDragEditTable,
  UniInput,
  UniInputNumber,
  UniInputNumberRange,
  UniSelect,
  UniTable,
  UniCheckbox,
  UniDateRadioPicker,
  Switch,
  UniSelectTextInput,
} from '@uni/components/src';
import { DatePicker, Input, InputNumber } from 'antd';
import UniNull from '@uni/components/src/empty';
import {
  UniHeaderFormDatePicker,
  UniHeaderFormRangePicker,
  UniHeaderFormSelect,
} from '@/layouts/form-datepicker';
import UniDependencySelect from '@uni/components/src/select/dependency-select';
import DateRangeWithType from '@uni/components/src/date-range-with-type';

export const dynamicComponentsMap = {
  UniSelect: UniSelect,
  UniConstrainedSelect: UniHeaderFormSelect,
  UniDatePicker: UniHeaderFormDatePicker,
  UniRangePicker: UniHeaderFormRangePicker,
  UniInput: UniInput,
  UniInputNumber: UniInputNumber,
  UniInputNumberRange,
  UniTable: UniTable,
  UniCheckbox,
  UniSwitch: Switch,
  // 空组件
  UniNull: UniNull,

  // 拖动table
  UniDragEditTable: UniDragEditTable,

  UniDateRadioPicker,

  UniDependencySelect: UniDependencySelect,
  UniConstrainedDependencySelect: UniDependencySelect,

  // 带有出院时间 / 登记时间的 时间选择
  UniDateRangeWithType: DateRangeWithType,

  UniSelectTextInput,
};
