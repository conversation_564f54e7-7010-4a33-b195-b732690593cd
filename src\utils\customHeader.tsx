import dayjs from 'dayjs';
import { BaseLayoutHeaderItem } from '@/layouts/base-layout';
import { getDayjsEndOfByType, getDayjsStartOfByType } from '@/utils/utils';
import { TracerTextInputSelectOptionsForSearch } from './customHeaderConstants';

/**
 * 获取模块配置
 * @param moduleName 模块名称
 * @param configKey 配置键名
 * @param defaultValue 默认值
 * @returns 配置值
 */
const getModuleConfig = (
  moduleName: string,
  configKey: string,
  defaultValue: any = false,
): any => {
  return (
    (window as any).externalConfig?.[moduleName]?.[configKey] ?? defaultValue
  );
};

/**
 * 创建动态 header 项
 * @param baseItems 基础 header 项
 * @param conditionalItems 条件性的 header 项
 * @param condition 条件
 * @returns 动态 header 项
 */
const createDynamicHeaderItems = (
  baseItems: BaseLayoutHeaderItem[],
  conditionalItems: BaseLayoutHeaderItem[],
  condition: boolean,
): BaseLayoutHeaderItem[] => {
  return condition ? [...baseItems, ...conditionalItems] : baseItems;
};

const baseHeaderComponents: { [key: string]: BaseLayoutHeaderItem } = {
  datePicker: {
    label: '时间',
    required: true,
    componentName: 'RangePicker',
    needFetch: false,
    props: {
      className: '',
      dataKey: 'date-range',
      valueKey: 'dateRange',
      dataType: 'date',
      picker: 'month',
      allowClear: 'true',
      valuePreProcess: (value) => {
        console.log(value);
        return value?.map((item) => {
          if (item) {
            return dayjs(item);
          }

          return item;
        });
      },
      postProcess: (currentValue, selectedValue, extra = undefined) => {
        return selectedValue?.map((item, index) => {
          if (item) {
            if (index === 0) {
              return getDayjsStartOfByType(extra?.picker, dayjs(item)).format(
                'YYYY-MM-DD',
              );
            } else {
              return getDayjsEndOfByType(extra?.picker, dayjs(item)).format(
                'YYYY-MM-DD',
              );
            }
          }

          return item;
        });
      },
    },
  },
  singleDatePicker: {
    label: '时间',
    required: true,
    componentName: 'DatePicker',
    needFetch: false,
    props: {
      className: '',
      dataKey: 'single-date',
      valueKey: 'singleDate',
      dataType: 'date',
      picker: 'date',
      allowClear: 'true',
      valuePreProcess: (value) => {
        return value ? dayjs(value) : value;
      },
      postProcess: (currentValue, selectedValue, extra = undefined) => {
        return selectedValue
          ? dayjs(selectedValue).format('YYYY-MM-DD')
          : selectedValue;
      },
    },
  },

  radioDatePicker: {
    // label: '时间',
    required: true,
    componentName: 'DateRadioPicker',
    needFetch: false,
    props: {
      className: '',
      dataKey: 'radio-date-range',
      valueKeys: ['dateType', 'dateRange'],
      formKey: 'dateRange',
      dataType: 'date',
      allowClear: true,
      valuePreProcess: (values) => {
        if (values?.length === 2) {
          values[1] = values?.at(1)?.map((item) => {
            if (item) {
              return dayjs(item);
            }
            return item;
          });
        }
        return values;
      },
      postProcess: (currentValues, changedValues, extra = undefined) => {
        return extra?.valueKeys?.map((d, i) => {
          if (d === 'dateRange') {
            return {
              [d]: changedValues?.[d]?.map((item, index) => {
                if (item) {
                  if (index === 0) {
                    return getDayjsStartOfByType(
                      extra?.picker === 'byValue'
                        ? changedValues?.['dateType']
                        : extra?.picker,
                      dayjs(item),
                    ).format('YYYY-MM-DD');
                  } else {
                    return getDayjsEndOfByType(
                      extra?.picker === 'byValue'
                        ? changedValues?.['dateType']
                        : extra?.picker,
                      dayjs(item),
                    ).format('YYYY-MM-DD');
                  }
                }

                return item;
              }),
            };
          }
          // 其他的直接返回
          return { [d]: changedValues?.[d] };
        });
      },
    },
  },

  selectTextInput: {
    componentName: 'SelectTextInput',
    needFetch: false,
    props: {
      dataKey: 'select-text-input',
      valueKeys: ['textSelectValue', 'textInputValue'],
      formKey: 'select-text-input',
      allowClear: true,
      selectOptions: TracerTextInputSelectOptionsForSearch,
      postProcess: (currentValues, changedValues, extra = undefined) => {
        console.log(
          'selectTextInput postProcess',
          currentValues,
          changedValues,
          extra,
        );
        return changedValues;
      },
    },
  },

  regionSelect: {
    label: '区域',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Region',
      valueKey: 'regionCodes',
      mode: 'multiple',
      placeholder: '请选择区域',
      optionValueKey: 'code',
    },
  },

  hospitalType: {
    label: '医院类型',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'HospType',
      valueKey: 'hospTypes',
      mode: 'multiple',
      placeholder: '请选择医院类型',
      optionValueKey: 'code',
    },
  },

  hospitalLevel: {
    label: '医院级别',
    componentName: 'Select',
    // needFetch: true,
    props: {
      dataKey: 'HospClass',
      valueKey: 'hospClasses',
      mode: 'multiple',
      placeholder: '请选择医院级别',
      optionValueKey: 'code',
    },
  },

  hospitalName: {
    label: '院区',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Hospital',
      valueKey: 'hospCodes',
      mode: 'multiple',
      placeholder: '请选择院区',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dataSourcePreProcess: (searchValue, dataSource) => {
        if (
          searchValue['regionCodes'] &&
          searchValue['regionCodes']?.length > 0
        ) {
          dataSource = dataSource?.filter((item) =>
            searchValue['regionCodes'].includes(item?.infos?.RegionCode),
          );
        }

        if (searchValue['hospTypes'] && searchValue['hospTypes']?.length > 0) {
          dataSource = dataSource?.filter((item) =>
            searchValue['hospTypes'].includes(item?.infos?.HospType),
          );
        }

        if (
          searchValue['hospClasses'] &&
          searchValue['hospClasses']?.length > 0
        ) {
          dataSource = dataSource?.filter((item) =>
            searchValue['hospClasses'].includes(item?.infos?.HospClass),
          );
        }

        return dataSource;
      },
    },
  },

  hospitalNameOne: {
    label: '院区',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Hospital',
      valueKey: 'hospCode',
      placeholder: '请选择院区',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dataSourcePreProcess: (searchValue, dataSource) => {
        if (
          searchValue['regionCodes'] &&
          searchValue['regionCodes']?.length > 0
        ) {
          dataSource = dataSource?.filter((item) =>
            searchValue['regionCodes'].includes(item?.infos?.RegionCode),
          );
        }

        if (searchValue['hospTypes'] && searchValue['hospTypes']?.length > 0) {
          dataSource = dataSource?.filter((item) =>
            searchValue['hospTypes'].includes(item?.infos?.HospType),
          );
        }

        if (
          searchValue['hospClasses'] &&
          searchValue['hospClasses']?.length > 0
        ) {
          dataSource = dataSource?.filter((item) =>
            searchValue['hospClasses'].includes(item?.infos?.HospClass),
          );
        }

        return dataSource;
      },
    },
  },

  // 动态部分专用
  // 会在最终使用时在customHeaders 替换dataKey以及valueKey
  odm_cliDeptsWithHospitalCode: {
    label: '科室（基于院区）',
    componentName: 'DependencySelect',
    needFetch: false,
    props: {
      dataKey: 'DynDepts',
      valueKey: 'dynDept',
      virtual: false,
      mode: 'single',
      placeholder: '请选择科室',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dependencyFormKeys: [
        {
          key: 'hospCodes',
          valueKey: 'ExtraProperties.HospCode',
        },
      ],
      dependencyValueNullEmptyData: true,
      // dataSourcePreProcess: (searchValue, dataSource) => {
      //   if (searchValue['hospCodes'] && searchValue['hospCodes']?.length > 0) {
      //     return dataSource?.filter((item) =>
      //       searchValue['hospCodes'].includes(item?.ExtraProperties?.HospCode),
      //     );
      //   } else {
      //     return [];
      //   }
      // },
    },
  },

  // 示踪通用科室 对于单个的匹配
  tracerCliDeptsWithHospitalCode: {
    label: '出院科室',
    componentName: 'Select',
    needFetch: false,
    props: {
      dataKey: 'Hierarchies',
      valueKey: 'CliDepts',
      mode: 'single',
      virtual: false,
      placeholder: '请选择科室',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dataSourcePreProcess: (searchValue, dataSource) => {
        if (searchValue['hospCode']) {
          return dataSource?.filter(
            (item) =>
              searchValue?.['hospCode']?.includes(item.HospCode) &&
              item.IsIpt === true,
          );
        } else {
          return dataSource?.filter((item) => item.IsIpt === true);
        }
      },
    },
  },
  //drg
  cardType: {
    label: '病案类型',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'CardType',
      valueKey: 'cardTypes',
      mode: 'multiple',
      placeholder: '请选择病案类型',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  operType: {
    label: '手术类别',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'OperType',
      valueKey: 'operType',
      mode: 'multiple',
      placeholder: '请选择手术类别',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  Sd: {
    label: '重点监控病种',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'SdType',
      valueKey: 'Sd',
      mode: 'multiple',
      placeholder: '请选择重点监控病种',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  deptName: {
    label: '科室',
    componentName: 'Select',
    needFetch: false,
    props: {
      dataKey: 'CliDepts',
      valueKey: 'deptCodes',
      mode: 'multiple',
      placeholder: '请选择科室',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dataSourcePreProcess: (searchValue, dataSource) => {
        //TODO
        return dataSource;
      },
    },
  },

  wardName: {
    label: '病区',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'Wards',
      valueKey: 'wardCodes',
      mode: 'multiple',
      placeholder: '请选择病区',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dataSourcePreProcess: (searchValue, dataSource) => {
        //TODO

        return dataSource;
      },
    },
  },

  // 受限病区
  constrainedWard: {
    label: '病区',
    componentName: 'ConstrainedSelect',
    needFetch: false,
    props: {
      dataKey: 'Wards',
      valueKey: 'wards',
      mode: 'multiple',
      placeholder: '请选择病区',
      allowClear: true,
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      enableSelectAll: true,
      dataSourcePreProcess: (searchValue, dataSource, userInfo) => {
        if (userInfo?.Wards?.length > 0) {
          return dataSource?.filter((data) =>
            (userInfo?.Wards || [])?.includes(data.Code),
          );
        }
        return dataSource;
      },
    },
  },

  // dyn-ddr/xxx/getlist 的status选项
  dynRegiStatus: {
    label: '状态',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'DynDynamicRegistrationStatus',
      valueKey: 'dynRegiStatus',
      mode: 'multiple',
      placeholder: '请选择状态',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },
  hierarchyName: {
    label: '层级',
    componentName: 'Input',
    needFetch: false,
    props: {
      dataKey: 'hierarchyCode',
      valueKey: 'hierarchyCode',
      mode: 'multiple',
      placeholder: '请选择层级',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
    },
  },

  // /mr 示踪很多重复的就提出来了
  combineSearch: {
    label: '病案标识',
    componentName: 'Input',
    needFetch: false,
    props: {
      dataKey: 'searchKeyword',
      valueKey: 'searchKeyword',
      placeholder: '病案号/条码号/姓名',
      allowClear: true,
    },
  },
  patNo: {
    label: '病案号',
    componentName: 'Input',
    needFetch: false,
    props: {
      // dataKey: 'PatNo',
      valueKey: 'patNo',
      placeholder: '请填写病案号',
    },
  },
  patName: {
    label: '姓名',
    componentName: 'Input',
    needFetch: false,
    props: {
      // dataKey: 'PatName',
      valueKey: 'patName',
      placeholder: '请填写姓名',
    },
  },
  barCode: {
    label: '条码号',
    componentName: 'Input',
    needFetch: false,
    props: {
      // dataKey: 'BarCode',
      valueKey: 'barCode',
      placeholder: '请填写条码号',
    },
  },
  signInOperator: {
    label: '签收人',
    componentName: 'Select',
    needFetch: true,
    props: {
      dataKey: 'SignInOperator',
      valueKey: 'signInOperator',
      dataKeyGroup: 'Mr',
      mode: undefined,
      placeholder: '请选择签收人',
      optionValueKey: 'Code',
      optionNameKey: 'Name',
      dataSourcePreProcess: (searchValue, dataSource) => {
        //TODO
        return dataSource;
      },
    },
  },
};

export const customHeaders = {
  ODM_common: [
    {
      ...baseHeaderComponents['datePicker'],
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    baseHeaderComponents['hospitalName'],
    baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
    baseHeaderComponents['dynRegiStatus'],
  ],
  ODM_inpatient: [
    {
      ...baseHeaderComponents['singleDatePicker'],
      required: true,
      label: '时间',
      props: {
        ...baseHeaderComponents['singleDatePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        allowClear: false,
        placeholder: '院区为必填',
      },
    },
    // baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
    // baseHeaderComponents['dynRegiStatus'],
  ],
  ODM_inHospManagement: [
    {
      ...baseHeaderComponents['datePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
    },
  ],
  ODM_dept: [
    {
      ...baseHeaderComponents['datePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        allowClear: true,
        placeholder: '院区为必填',
      },
    },
    {
      ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
      required: true,
      props: {
        ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'].props,
      },
    },
  ],
  ODM_opt_dept: [
    {
      ...baseHeaderComponents['datePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        allowClear: true,
        placeholder: '院区为必填',
      },
    },
    {
      ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
      required: true,
      props: {
        ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'].props,
        dataKey: 'OtpDepts',
        valueKey: 'otpDept',
      },
    },
  ],
  ODM_obs_dept: [
    {
      ...baseHeaderComponents['datePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      required: true,
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        allowClear: true,
        placeholder: '院区为必填',
      },
    },
    {
      ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
      required: true,
      props: {
        ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'].props,
        dataKey: 'ObsDepts',
        valueKey: 'obsDept',
        defaultValueType: 'firstValueSingle', // 特殊字段 用于默认值
      },
    },
  ],
  ODM_workloadItem: [
    {
      ...baseHeaderComponents['hospitalNameOne'],
      required: true,
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalNameOne'].props,
        placeholder: '院区为必填',
      },
    },
  ],
  ODM_hierarchyBed: (() => {
    // 基础 header 项（非v2版本的完整配置）
    const baseItems: BaseLayoutHeaderItem[] = [
      {
        ...baseHeaderComponents['datePicker'],
        props: {
          ...baseHeaderComponents['datePicker'].props,
          allowClear: false,
          picker: 'date',
        },
      },
      {
        ...baseHeaderComponents['hospitalName'],
        props: {
          ...baseHeaderComponents['hospitalName'].props,
        },
      },
      {
        ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
        label: '医疗单元名称',
        props: {
          ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'].props,
          valueKey: 'hierarchyCodes',
          maxTagCount: 'responsive',
          mode: 'multiple',
        },
      },
    ];

    // v2版本的header项（只需要hospitalName）
    const v2Items: BaseLayoutHeaderItem[] = [
      {
        ...baseHeaderComponents['hospitalName'],
        props: {
          ...baseHeaderComponents['hospitalName'].props,
        },
      },
    ];

    // 读取配置决定使用哪个版本
    const useV2Page = getModuleConfig(
      'operationalDataManagement',
      'useV2Page',
      false,
    );

    // 根据配置动态创建 header 项
    return useV2Page ? v2Items : baseItems;
  })(),
  ODM_hierarchyBed_obs: (() => {
    // 基础 header 项（非v2版本的完整配置）
    const baseItems: BaseLayoutHeaderItem[] = [
      {
        ...baseHeaderComponents['datePicker'],
        props: {
          ...baseHeaderComponents['datePicker'].props,
          allowClear: false,
          picker: 'date',
        },
      },
      {
        ...baseHeaderComponents['hospitalName'],
        props: {
          ...baseHeaderComponents['hospitalName'].props,
        },
      },
      {
        ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
        label: '医疗单元名称',
        props: {
          ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'].props,
          dataKey: 'ObsDepts',
          valueKey: 'ObsHierarchyCodes',
          maxTagCount: 'responsive',
          mode: 'multiple',
        },
      },
    ];

    // v2版本的header项（只需要hospitalName）
    const v2Items: BaseLayoutHeaderItem[] = [
      {
        ...baseHeaderComponents['hospitalName'],
        props: {
          ...baseHeaderComponents['hospitalName'].props,
        },
      },
    ];

    // 读取配置决定使用哪个版本
    const useV2Page = getModuleConfig(
      'operationalDataManagement',
      'useV2Page',
      false,
    );

    // 根据配置动态创建 header 项
    return useV2Page ? v2Items : baseItems;
  })(),
  ODM_hospEmployee: [
    baseHeaderComponents['hospitalName'],
    baseHeaderComponents['dynRegiStatus'],
  ],
  ODM_proofread_dynamic: (() => {
    // 基础 header 项
    const baseItems: BaseLayoutHeaderItem[] = [
      {
        ...baseHeaderComponents['datePicker'],
        props: {
          ...baseHeaderComponents['datePicker'].props,
          allowClear: false,
          picker: 'date',
        },
      },
      {
        ...baseHeaderComponents['hospitalNameOne'],
        required: true,
        label: '院区',
        props: {
          ...baseHeaderComponents['hospitalNameOne'].props,
          placeholder: '院区为必填',
          allowClear: false,
          mode: 'single',
        },
      },
    ];

    // V2 版本额外的 header 项
    const v2Items: BaseLayoutHeaderItem[] = [
      {
        ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
        label: '科室',
        required: true,
        props: {
          ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'].props,
          valueKey: 'dynDepts',
          mode: 'multiple',
          maxTagCount: 'responsive',
          enableSelectAll: true,
          dependencyFormKeys: [
            {
              key: 'hospCode',
              valueKey: 'ExtraProperties.HospCode',
            },
          ],
        },
      },
    ];

    // 读取配置决定使用哪个版本
    const useV2Page = getModuleConfig(
      'operationalDataManagement',
      'useV2Page',
      false,
    );

    // 根据配置动态创建 header 项
    return createDynamicHeaderItems(baseItems, v2Items, useV2Page);
  })(),
  ODM_dailyProofread: [
    {
      ...baseHeaderComponents['singleDatePicker'],
      required: true,
      props: {
        ...baseHeaderComponents['singleDatePicker'].props,
        allowClear: false,
      },
    },
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
      },
    },
    {
      ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'],
      label: '科室',
      required: true,
      props: {
        ...baseHeaderComponents['odm_cliDeptsWithHospitalCode'].props,
        // dataKeyGroup: 'Mr',
        valueKey: 'dynDepts',
        mode: 'multiple',
        maxTagCount: 'responsive',
        enableSelectAll: true,
        // dependencyFormKeys: [
        //   {
        //     key: 'hospCodes',
        //     valueKey: 'ExtraProperties.HospCode',
        //   },
        // ],
        // dependencyValueNullEmptyData: true,
      },
    },
  ],

  // 示踪：总体列表
  tracer_traceRecord: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'],
      label: '出院科室',
      props: {
        ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'].props,
        // dataKeyGroup: 'Mr',
        valueKey: 'outDept',
        mode: undefined,
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
    {
      label: '签收状态',
      componentName: 'Select',
      needFetch: true,
      props: {
        dataKey: 'WorkFlowStatus',
        valueKey: 'workFlowStatus',
        mode: undefined,
        placeholder: '请选择签收状态',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          //TODO
          return dataSource;
        },
      },
    },
  ],
  // 示踪：病区病案
  tracer_wardSignined: [
    {
      ...baseHeaderComponents['constrainedWard'],
      label: '出院病区',
      props: {
        ...baseHeaderComponents['constrainedWard'].props,
        placeholder: '请选择出院病区',
        // valueKey: 'Wards',
        // mode: 'single',
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      label: '签收人',
      componentName: 'Select',
      needFetch: true,
      props: {
        dataKey: 'WardSignOutOperator',
        valueKey: 'WardSignOutOperator',
        // mode: 'multiple',
        placeholder: '请选择签收人',
        mode: 'single',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          //TODO

          return dataSource;
        },
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '签收日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        valueKey: 'WardSignOutRange',
        picker: 'date',
      },
    },
  ],
  // 示踪：病案 查询
  tracer_mrRoomSignIned: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'],
      label: '出院科室',
      props: {
        ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'].props,
        // dataKeyGroup: 'Mr',
        valueKey: 'outDept',
        mode: undefined,
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
    baseHeaderComponents['signInOperator'],
    // {
    //   ...baseHeaderComponents['singleDatePicker'],
    //   label: '签收日期',
    //   required: false,
    // },
    {
      ...baseHeaderComponents['datePicker'],
      label: '签收日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        valueKey: 'SignInRange',
        picker: 'date',
      },
    },
    {
      label: '签收天数',
      componentName: 'InputNumberRange',
      needFetch: false,
      props: {
        valueKey: 'signInDays',
        placeholder: ['请填写最小天数', '请填写最大天数'],
      },
    },
  ],
  // 示踪：病案签收统计-按签收日期
  tracer_signInStatisticsBySignInDate: [
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
      },
    },
    {
      ...baseHeaderComponents['singleDatePicker'],
      label: '签收日期',
    },
    {
      ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'],
      label: '出院科室',
      props: {
        ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'].props,
        // dataKeyGroup: 'Mr',
        valueKey: 'outDept',
        mode: undefined,
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
    baseHeaderComponents['signInOperator'],
    {
      label: '签收天数',
      componentName: 'InputNumberRange',
      needFetch: false,
      props: {
        valueKey: 'signInDays',
        placeholder: ['请填写最小天数', '请填写最大天数'],
      },
    },
  ],
  // 示踪：病案签收统计-按出院日期
  tracer_signInStatisticsByOutDate: [
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'],
      label: '出院科室',
      props: {
        ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'].props,
        // dataKeyGroup: 'Mr',
        valueKey: 'outDept',
        mode: undefined,
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
    baseHeaderComponents['signInOperator'],
    {
      label: '签收天数',
      componentName: 'InputNumberRange',
      needFetch: false,
      props: {
        valueKey: 'signInDays',
        placeholder: ['请填写最小天数', '请填写最大天数'],
      },
    },
  ],
  //示踪：归档
  tracer_archived: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'],
      label: '出院科室',
      props: {
        ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'].props,
        // dataKeyGroup: 'Mr',
        valueKey: 'outDept',
        mode: undefined,
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
    {
      label: '库房选择',
      componentName: 'Select',
      needFetch: true,
      props: {
        dataKey: 'Warehouse',
        valueKey: 'wareHouseNo',
        mode: 'single',
        placeholder: '请选择库房',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          //TODO
          return dataSource;
        },
      },
    },
    {
      label: '库位选择',
      componentName: 'Select',
      needFetch: true,
      props: {
        dataKey: 'InventoryLocation',
        valueKey: 'inventoryLocation',
        mode: 'single',
        placeholder: '请选择库位',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          //TODO

          return dataSource;
        },
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '归档日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        valueKey: 'ArchivedRange',
        picker: 'date',
      },
    },
  ],
  // 示踪：封存
  tracer_sealing: [
    baseHeaderComponents['selectTextInput'],
    baseHeaderComponents['patName'],
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
  ],
  tracer_sealed: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['patNo'],
      label: '病案号',
      props: {
        ...baseHeaderComponents['patNo'].props,
        placeholder: '请填写病案号',
      },
    },
    baseHeaderComponents['patName'],
    {
      ...baseHeaderComponents['datePicker'],
      label: '封存日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        valueKey: 'sealDateRange',
        picker: 'date',
      },
    },
  ],
  // 示踪：借阅
  tracer_borrowed: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'],
      label: '出院科室',
      props: {
        ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'].props,
        // dataKeyGroup: 'Mr',
        valueKey: 'outDept',
        mode: undefined,
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '借阅日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        valueKey: 'borrowDateRange',
        picker: 'date',
      },
    },
    {
      label: '借阅者',
      componentName: 'Select',
      needFetch: false,
      props: {
        dataKeyGroup: 'Mr',
        dataKey: 'Employee',
        valueKey: 'Borrower',
        mode: 'single',
        placeholder: '请填写借阅人',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          //TODO
          return dataSource;
        },
      },
    },
    {
      label: '联系方式',
      componentName: 'Input',
      needFetch: false,
      props: {
        dataKey: 'contactInfo',
        valueKey: 'contactInfo',
        placeholder: '请填写联系方式',
      },
    },
    // {
    //   label: '借阅超过天数',
    //   componentName: 'InputNumber',
    //   needFetch: false,
    //   props: {
    //     dataKey: 'BorrowDays',
    //     valueKey: 'borrowDays',
    //     placeholder: '请填写天数',
    //   },
    // },
    {
      label: '是否返还',
      componentName: 'Switch',
      needFetch: false,
      props: {
        dataKey: 'IsReturned',
        valueKey: 'isReturned',
        className: 'header_switch',
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
    },
  ],
  tracer_return: [
    baseHeaderComponents['barCode'],
    baseHeaderComponents['patNo'],
  ],
  //示踪：催缴
  tracer_askForPay: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'],
      label: '出院科室',
      props: {
        ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'].props,
        // dataKeyGroup: 'Mr',
        valueKey: 'outDept',
        mode: undefined,
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
    {
      label: '超期天数',
      componentName: 'InputNumber',
      needFetch: false,
      props: {
        valueKey: 'exceedDays',
        placeholder: '请填写天数',
      },
    },
  ],
  // 示踪：催还
  tracer_askForReturn: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '借阅日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        valueKey: 'borrowDateRange',
        picker: 'date',
      },
    },
    {
      label: '借阅者',
      componentName: 'Select',
      needFetch: false,
      props: {
        dataKeyGroup: 'Mr',
        dataKey: 'Employee',
        valueKey: 'Borrower',
        mode: 'single',
        placeholder: '请填写借阅人',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          //TODO
          return dataSource;
        },
      },
    },
    {
      label: '联系方式',
      componentName: 'Input',
      needFetch: false,
      props: {
        dataKey: 'contactInfo',
        valueKey: 'contactInfo',
        placeholder: '请填写联系方式',
      },
    },
    {
      label: '超期天数',
      componentName: 'InputNumber',
      needFetch: false,
      props: {
        valueKey: 'exceedDays',
        placeholder: '请填写天数',
      },
    },
    {
      label: '是否返还',
      componentName: 'Switch',
      needFetch: false,
      props: {
        dataKey: 'IsReturned',
        valueKey: 'isReturned',
        className: 'header_switch',
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
    },
  ],
  // 示踪 复印 查询
  tracer_printTraceRecord: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['hospitalNameOne'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalNameOne'].props,
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    // {
    //   ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'],
    //   label: '出院科室',
    //   props: {
    //     ...baseHeaderComponents['tracerCliDeptsWithHospitalCode'].props,
    //     // dataKeyGroup: 'Mr',
    //     valueKey: 'outDept',
    //     mode: undefined,
    //   },
    // },
    // {
    //   ...baseHeaderComponents['deptName'],
    //   label: '责任科室',
    //   props: {
    //     ...baseHeaderComponents['deptName'].props,
    //     // dataKeyGroup: 'Mr',
    //     dataKey: 'MrDepts',
    //     valueKey: 'dutyDept',
    //     mode: undefined,
    //     //TODO dataSourcePreProcess
    //   },
    // },
    // baseHeaderComponents['signInOperator'],
    {
      ...baseHeaderComponents['datePicker'],
      label: '复印日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        valueKey: 'PrintRange',
        picker: 'date',
      },
    },
    // {
    //   label: '状态',
    //   componentName: 'Select',
    //   needFetch: true,
    //   props: {
    //     dataKey: 'DynDynamicRegistrationStatus',
    //     valueKey: 'dynRegiStatus',
    //     mode: 'multiple',
    //     placeholder: '请选择状态',
    //     optionValueKey: 'Code',
    //     optionNameKey: 'Name',
    //   },
    // },
  ],
  // 示踪：病案室出库
  tracer_dmrSignOut: [
    baseHeaderComponents['selectTextInput'],
    {
      ...baseHeaderComponents['hospitalName'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalName'].props,
        valueKey: 'hospCode',
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['constrainedWard'],
      label: '出院病区',
      componentName: 'DependencySelect',
      props: {
        ...baseHeaderComponents['constrainedWard'].props,
        valueKey: 'outWard',
        mode: undefined,
        dependencyFormKeys: [
          {
            key: 'hospCode',
            valueKey: 'ExtraProperties.HospCode',
          },
        ],
        dependencyValueNullEmptyData: true,
      },
    },
    {
      label: '出库人',
      componentName: 'Select',
      needFetch: true,
      props: {
        dataKey: 'DmrSignOutOperator',
        valueKey: 'DmrSignOutOperator',
        // mode: 'multiple',
        placeholder: '请选择出库人',
        mode: 'single',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          //TODO

          return dataSource;
        },
      },
    },
    {
      ...baseHeaderComponents['datePicker'],
      label: '出库日期',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        valueKey: 'dmrSignOutDateRange',
        picker: 'date',
      },
    },
    {
      label: '是否出库',
      componentName: 'Switch',
      needFetch: false,
      props: {
        dataKey: 'IsDmrSignedOut',
        valueKey: 'IsDmrSignedOut',
        className: 'header_switch',
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
    },
  ],

  // 示踪：签收归档分析
  tracer_anal: [
    {
      ...baseHeaderComponents['datePicker'],
      label: '出院日期',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        allowClear: false,
        picker: 'date',
      },
    },
    {
      ...baseHeaderComponents['hospitalNameOne'],
      label: '院区',
      props: {
        ...baseHeaderComponents['hospitalNameOne'].props,
      },
    },
    // {
    //   ...baseHeaderComponents['hospitalNameOne'],
    //   label: '院区',
    //   props: {
    //     ...baseHeaderComponents['hospitalNameOne'].props,
    //   },
    // },
    {
      ...baseHeaderComponents['deptName'],
      label: '责任科室',
      props: {
        ...baseHeaderComponents['deptName'].props,
        // dataKeyGroup: 'Mr',
        dataKey: 'MrDepts',
        valueKey: 'dutyDept',
        mode: undefined,
        //TODO dataSourcePreProcess
      },
    },
  ],

  tracer_borrow_tasks: [
    {
      ...baseHeaderComponents['datePicker'],
      label: '借阅时间',
      required: false,
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
        required: false,
      },
    },
    {
      label: '借阅者',
      componentName: 'Select',
      needFetch: false,
      props: {
        dataKeyGroup: 'Mr',
        dataKey: 'Employee',
        valueKey: 'Borrower',
        mode: 'single',
        placeholder: '请填写借阅人',
        optionValueKey: 'Code',
        optionNameKey: 'Name',
        dataSourcePreProcess: (searchValue, dataSource) => {
          //TODO
          return dataSource;
        },
      },
    },
    {
      label: '清单号',
      componentName: 'Input',
      props: {
        valueKey: 'ApplicationId',
        placeholder: '请填写清单号',
      },
    },
  ],

  tracer_user_borrow_tasks: [
    {
      ...baseHeaderComponents['datePicker'],
      label: '借阅时间',
      props: {
        ...baseHeaderComponents['datePicker'].props,
        picker: 'date',
      },
    },
  ],

  settleInfo: [
    baseHeaderComponents['datePicker'],
    {
      label: '主诊编码',
      componentName: 'Input',
      needFetch: false,
      props: {
        className: 'settleInfo-input',
        valueKey: 'dscgDiagCode',
        placeholder: '请填写主诊编码',
      },
    },
    {
      label: '主手术编码',
      componentName: 'Input',
      needFetch: false,
      props: {
        className: 'settleInfo-input',
        valueKey: 'mainOperCode',
        placeholder: '请填写主手术编码',
      },
    },
    {
      label: '医保分组编码',
      componentName: 'Input',
      needFetch: false,
      props: {
        className: 'settleInfo-input',
        valueKey: 'chsDrgCode',
        placeholder: '请填写医保分组编码',
      },
    },
  ],

  // 医保基金监控
  radioDatePickerTest: [baseHeaderComponents['radioDatePicker']],

  // reportSys/externalCalcRecord
  externalCalcRecord: [
    {
      label: '唯一标识',
      componentName: 'Input',
      needFetch: false,
      props: {
        valueKey: 'hisId',
        placeholder: '请填写唯一标识',
      },
    },
    {
      ...baseHeaderComponents['singleDatePicker'],
      label: '最早请求时间',
      required: false,
      props: {
        ...baseHeaderComponents['singleDatePicker']?.props,
        valueKey: 'minDateTime',
        placeholder: '请填写最早请求时间',
      },
    },
  ],
};
