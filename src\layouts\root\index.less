@import '~@uni/commons/src/style/variables.less';

.browser-check-container {
  .ant-modal-close {
    display: none;
  }

  .browser-hint-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .warn-text {
      font-size: 16px;
      font-weight: bold;
      color: @red-color;
      margin-bottom: 10px;
      margin-top: 10px;
    }

    .other-text {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 10px;
    }
  }
}

#root-master {
  height: 100%;
}

.root-container {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  min-width: 768px;
  overflow-y: auto;
  overflow-x: auto;
}

.content-container {
  flex: auto;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .site-layout-content {
    background-color: transparent;
    margin: 10px 0px 0px 8px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;

    .ant-breadcrumb {
      margin-bottom: 10px;
    }
  }

  .content {
    height: 100%;
    max-height: 100%;
    overflow-y: auto;
    padding-right: 12px;
    width: 100%;
    overflow-x: hidden;

    & > .ant-layout:first-child {
      height: 100%;

      & .qiankun-micro-app-wrapper:first-child {
        height: 100%;
      }
    }
  }

  .site-layout-footer {
    // border-top: 2px solid @light-blue-color;
    text-align: center;
    background-color: transparent;
    margin: 0px 0px;
    padding: 12px 50px !important;

    &:hover {
      animation: shake-bottom 0.5s cubic-bezier(0.455, 0.03, 0.515, 0.955) both;
    }
  }

  .dmr-aod-container {
    > .ant-modal-root > .ant-modal-wrap {
      > .ant-modal {
        top: 0;
        max-width: 100%;
        height: 100%;
        padding-bottom: 0px;

        > .ant-modal-content {
          height: 100%;
          border-radius: 0px;

          > .ant-modal-body {
            padding: 0;
            height: 100%;
          }
        }
      }
    }
  }

  // 医生首页病案明细查询
  .dmr-index-container {
    > .ant-modal-root > .ant-modal-wrap {
      > .ant-modal {
        top: 0;
        max-width: 100%;
        height: 100%;
        padding-bottom: 0px;

        > .ant-modal-content {
          height: 100%;
          border-radius: 0px;

          > .ant-modal-body {
            padding: 0;
            height: 100%;
          }
        }
      }
    }
  }

  .dmr-aod-review-score-container {
    .ant-modal-wrap {
      width: calc(62% - 12px);
      top: 55px;
      height: calc(100% - 60px - 55px - 27px);
      overflow-y: auto;
    }
  }

  .dmr-aod-review-comment-container {
    .ant-modal-wrap {
      width: 76%;
      top: 55px;
      height: calc(100% - 60px - 55px - 9px);
      overflow-y: auto;

      .dmr-container {
        width: 100% !important;
      }
    }
  }

  .dmr-aod-review-container {
    #dmr-base-container {
      height: 100%;
    }

    .dmr-header-container {
      padding-left: 0 !important;
    }

    .dmr-content-container {
      padding: 0px 5px 0px 5px;
    }

    .separator-container {
      width: calc(100% - 10px);
    }

    ::-webkit-scrollbar {
      width: 6px;
      background-color: transparent;
      position: absolute;
      z-index: 100;
      right: 0;
    }

    ::-webkit-scrollbar:horizontal {
      height: 6px;
      bottom: 0;
    }

    ::-webkit-scrollbar-thumb {
      position: absolute;
      z-index: 101;
      border-radius: 20px;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      background-color: rgb(189, 189, 189);
    }

    ::-webkit-scrollbar-thumb:horizontal {
      border-radius: 20px;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      background-color: rgb(189, 189, 189);
    }

    ::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      background-color: #f5f5f5;
    }
  }
}

.error-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .ant-collapse {
    width: 600px;
  }

  .ant-result-content {
    background-color: transparent;
  }

  .message {
    display: block;
    margin-bottom: 4px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
  }

  .ant-collapse-content-box {
    height: 300px;
    overflow-y: auto;
  }
}
