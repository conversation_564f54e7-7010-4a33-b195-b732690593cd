import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import React from 'react';
import { Tooltip } from 'antd';
import { EmptyWrapper } from '../index';
import { contentTextStyleProcessor } from './text-style';
import { getTooltipPopupContainer } from '../utils';

export const dataItemWithColumnModuleProcessor = (
  dictionaryData: any,
  module,
  dataItem,
) => {
  if (Array.isArray(dictionaryData?.[module]) && dictionaryData?.[module]) {
    let dictionaryItem = dictionaryData?.[module]?.find(
      (item) => item.Code === dataItem,
    );
    if (dictionaryItem) {
      return dictionaryItem?.Name || dataItem;
    }
  }

  return dataItem;
};

export const multipleTranslateDelimiter = '; ';

const dataItemWithColumnModuleProcessorWithMultipleValue = (
  dictionaryData: any,
  module,
  dataItem,
) => {
  let dataItems = dataItem?.toString()?.split(multipleTranslateDelimiter);
  let translatedItems = dataItems?.map((item) => {
    if (Array.isArray(dictionaryData?.[module]) && dictionaryData?.[module]) {
      let dictionaryItem = dictionaryData?.[module]?.find(
        (dictItem) => dictItem.Code === item?.trim(),
      );
      if (dictionaryItem) {
        return dictionaryItem?.Name || item;
      }
    }
    return item;
  });

  return translatedItems?.join(`${multipleTranslateDelimiter}`);
};

export const TanstackDictionaryProcessor = (
  transformedColumns: any[],
  dictionaryData?: any,
  enableMaxCharNumberEllipses?: boolean,
  columnMetaProperty?: string,
  maxEllipseCharNumber?: number,
  enableColumnResizing?: boolean,
  enableMultipleTranslate?: boolean,
) => {
  if (isEmptyValues(dictionaryData)) {
    return transformedColumns;
  }

  transformedColumns?.forEach((transformedColumnsItem) => {
    let columnItem = isEmptyValues(columnMetaProperty)
      ? transformedColumnsItem
      : transformedColumnsItem?.[columnMetaProperty];
    if (columnItem?.['normalRenderer']) {
      columnItem.render = (node, record, index) => {
        // module & moduleGroup
        let dataItem = record[columnItem.dataIndex];
        if (columnItem?.isExtraProperty === true) {
          dataItem = record?.['ExtraProperties']?.[columnItem.dataIndex];
        }
        if (columnItem?.dictionaryModule) {
          let currentDictionaryData = dictionaryData;
          if (columnItem?.dictionaryModuleGroup) {
            currentDictionaryData =
              dictionaryData?.[columnItem?.dictionaryModuleGroup];
          }

          if (enableMultipleTranslate === true) {
            dataItem = dataItemWithColumnModuleProcessorWithMultipleValue(
              currentDictionaryData,
              columnItem?.dictionaryModule,
              dataItem,
            );
          } else {
            dataItem = dataItemWithColumnModuleProcessor(
              currentDictionaryData,
              columnItem?.dictionaryModule,
              dataItem,
            );
          }
        }

        // 处理一下可能会出现的多个的场景
        let columnValue = '';
        const dataItemsSplitedDelimiter = dataItem
          ?.toString()
          ?.split(multipleTranslateDelimiter);
        if (dataItemsSplitedDelimiter?.length > 1) {
          let translatedDataItems = [];
          dataItemsSplitedDelimiter?.forEach((dataItem) => {
            translatedDataItems.push(
              valueNullOrUndefinedReturnDash(
                dataItem,
                columnItem['dataType'],
                columnItem['scale'],
              ),
            );
          });
          columnValue = translatedDataItems?.join(multipleTranslateDelimiter);
        } else {
          columnValue = valueNullOrUndefinedReturnDash(
            dataItem,
            columnItem['dataType'],
            columnItem['scale'],
          );
        }

        // console.log('row-cell directory', columnValue);

        let enableTooltip = false;
        let mergedEllipseStyle = contentTextStyleProcessor(columnItem);

        if (
          enableMaxCharNumberEllipses === true &&
          columnValue?.length > (maxEllipseCharNumber ?? 20)
        ) {
          enableTooltip = true;
        }

        if (enableColumnResizing === true) {
          enableTooltip = true;
        }

        const CellTooltipWrapper =
          enableTooltip === true ? Tooltip : EmptyWrapper;

        return (
          <CellTooltipWrapper
            title={columnValue}
            placement={'topLeft'}
            getPopupContainer={getTooltipPopupContainer}
          >
            <span style={mergedEllipseStyle}>
              {enableMaxCharNumberEllipses === true &&
              columnValue?.length > (maxEllipseCharNumber ?? 20)
                ? `${columnValue?.slice(0, maxEllipseCharNumber ?? 20)}...`
                : columnValue}
            </span>
          </CellTooltipWrapper>
        );
      };
    }
  });

  return transformedColumns;
};
