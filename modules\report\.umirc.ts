import { name } from './package.json';
import {
  slaveCommonConfig,
  extraBabelIncludes,
  extraWebPackPlugin,
  title,
} from '../../.umirc.commom';

export default {
  title: title,
  base: name,
  publicPath: '/report/',
  outputPath: '../../dist/report',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  qiankun: { slave: {} },

  plugins: [
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/hqms',
    },
    {
      path: '/hospital',
      exact: true,
      component: '@/pages/hospital/index',
    },
    {
      path: '/wt',
      exact: true,
      component: '@/pages/wt/index',
    },
    {
      path: '/hqms',
      exact: true,
      component: '@/pages/hqms/index',
    },
    {
      path: '/workLoadItem',
      exact: true,
      component: '@/pages/workLoadItem/index',
    },
    {
      path: '/public',
      exact: true,
      component: '@/pages/public/index',
    },
    {
      path: '/dmrQuality',
      exact: true,
      component: '@/pages/dmr-card-quality/index',
    },
    {
      path: '/highlight/:id',
      exact: true,
      component: '@/pages/highlight/index',
    },
    // his 系
    // drg
    {
      path: '/drg/report',
      exact: true,
      component: '@/pages/drg/report/index',
    },
    {
      path: '/drg/highlight/:id',
      // exact: true,
      component: '@/pages/drg/highlightReport/index',
    },
    // dip
    {
      path: '/dip/report',
      exact: true,
      component: '@/pages/dip/report/index',
    },
    {
      path: '/dip/highlight/:id',
      // exact: true,
      component: '@/pages/dip/highlightReport/index',
    },
    // 结算清单
    {
      path: '/insur/report',
      exact: true,
      component: '@/pages/insur/report/index',
    },
    {
      path: '/insur/highlight/:id',
      exact: true,
      component: '@/pages/insur/highlightReport/index',
    },
    {
      path: '/dmrQuality/highlight/:id',
      exact: true,
      component: '@/pages/dmr-card-quality/highlightReport/index',
    },
    // 评审
    {
      path: '/examine/report',
      exact: true,
      component: '@/pages/qualityExamine/index',
    },
    {
      path: '/examine/highlight/:id',
      // exact: true,
      component: '@/pages/qualityExamine/highlightReport/index',
    },
    // {

    // },
    // {

    // }
  ],
};
