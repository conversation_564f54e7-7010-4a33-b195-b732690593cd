import { isEmptyValues } from '@uni/utils/src/utils';
import _ from 'lodash';

const useHisV2 = (window as any).externalConfig?.his?.useV2 ?? false;

export default class RequestMiddleware {
  hisV2Urls = [
    'LatestDipSettleStats',
    'LatestSettleCheckReport',
    'LatestDrgSettleStats',
  ];

  options: any = {};

  constructor() {}

  process = async (options) => {
    this.options = _.cloneDeep(options);

    this.#useHisV2Middleware();

    return this.options;
  };

  #useHisV2Middleware = () => {
    // console.log('useHisV2Middleware', useHisV2);
    if (
      useHisV2 &&
      this.hisV2Urls?.some((item) => this.options?.url?.includes(item))
    ) {
      const regex = /\/api/i;
      // console.log('useHisV2Middleware', useHisV2, regex);
      if (regex.test(this.options?.url)) {
        const index = this.options?.url.search(regex);
        // console.log(
        //   'useHisV2Middleware',
        //   useHisV2,
        //   index,
        //   this.options,
        //   this.options?.url?.slice(0, index + 4) +
        //     '/v2' +
        //     this.options?.url?.slice(index + 4),
        // );

        this.options = {
          ...this.options,
          url:
            this.options?.url?.slice(0, index + 4) +
            '/v2' +
            this.options?.url?.slice(index + 4),
        };
      }
    }
  };
}
