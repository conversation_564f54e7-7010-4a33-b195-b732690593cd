import { useEffect, useState } from 'react';
import {
  AssignmentItem,
  BatchItem,
  BatchMasterItem,
} from '@/pages/review/interface';
import './index.less';
import { Form } from 'antd';
import { useModel, useRequest } from 'umi';
import { isEmptyValues } from '@uni/utils/src/utils';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import DetailTable from './detailTable';

interface ReviewManagementInfoProps {
  containerRef: any;
  taskTableRef: any;
  activeKey: string;
  batchId?: string;
  batchInfo?: BatchItem;
  dmrPreviewContainerRef?: any;
  dmrReviewerContainerRef?: any;
  assignments?: AssignmentItem[];

  tabTitleContainerRef?: any;

  extraConfig?: any;
  tabKey: string;
  searchParams?: any;
  selectedStatItem?: string;
}

const ReviewManagementInfo = (props: ReviewManagementInfoProps) => {
  const [form] = Form.useForm();
  const { searchParams } = props;
  const [batchMasters, setBatchMasters] = useState<BatchMasterItem[]>([]);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  useEffect(() => {
    getBatchMasterReq();
  }, []);

  useEffect(() => {
    if (props?.activeKey === 'REVIEW_INFO') {
      if (!isEmptyValues(props?.batchInfo)) {
        props?.taskTableRef?.current?.freshQueryTable();
      }
    }
  }, [searchParams, props?.activeKey]);

  const { loading: getBatchMasterLoading, run: getBatchMasterReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/QualityExamineSys/GetQualityExamineSettingMasters',
        {
          method: 'GET',
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setBatchMasters(response?.data);
        } else {
          setBatchMasters([]);
        }
      },
    },
  );

  return (
    <div className={'review-info-container'}>
      <DetailTable
        {...props?.extraConfig}
        form={form}
        selectedStatItem={props?.selectedStatItem}
        containerRef={props?.containerRef}
        searchParams={props?.searchParams}
        masterId={props?.batchInfo?.MasterId}
        batchId={props?.batchInfo?.BatchId?.toString()}
        batchInfo={props?.batchInfo}
        taskTableRef={props?.taskTableRef}
        dmrPreviewContainerRef={props?.dmrPreviewContainerRef}
        tabTitleContainerRef={props?.tabTitleContainerRef}
        dmrReviewerContainerRef={props?.dmrReviewerContainerRef}
      />
    </div>
  );
};

export default ReviewManagementInfo;
