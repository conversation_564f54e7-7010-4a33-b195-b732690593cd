import classNames from 'classnames';
import React from 'react';
import { RowProps } from '../interfaces';
import { flexRender } from '@tanstack/react-table';
import isEqual from 'lodash/isEqual';
import { Tooltip } from 'antd';
import { EmptyWrapper } from '../index';
import { contentTextStyleProcessor } from '../processor/text-style';
import {
  cellClassNameProcessor,
  cellStyleProcessor,
  getTooltipPopupContainer,
} from '../utils';
import { useDraggableTableAlongCell } from './header-drag';

export interface CommonRowProps extends RowProps {
  rowRefs: any;
  rowKey: string;
  row: any;
  children?: React.ReactNode;
  virtualizeProps?: any;

  style?: React.CSSProperties;
  className?: string;

  formItemContainerClassName?: string;

  formKey: string;

  onRowClick?: (record: any, rowIndex: number) => void;
  rowVirtualizer?: any;
  tableWidth?: number;
}

const commonRowPropsEqual = (oldProps: any, newProps: any) => {
  console.log(
    'commonRowPropsEqual',
    oldProps?.virtualizeProps,
    newProps?.virtualizeProps,
    isEqual(oldProps?.virtualizeProps?.style, newProps?.virtualizeProps?.style),
  );

  return (
    isEqual(oldProps?.tableWidth, newProps?.tableWidth) &&
    isEqual(oldProps?.row, newProps?.row) &&
    isEqual(
      oldProps?.virtualizeProps?.style,
      newProps?.virtualizeProps?.style,
    ) &&
    isEqual(oldProps?.rowClassName, newProps?.rowClassName)
  );
};

export const CommonRow = (props: CommonRowProps) => {
  // console.log('column row', props?.row);

  return (
    <tr
      ref={(node) => {
        props.rowRefs.current[props?.row?.index] = node;
        return props?.rowVirtualizer?.measureElement(node);
      }}
      tabIndex={-1}
      // key={props?.row?.[props?.rowKey]}
      data-row-key={props?.row?.[props?.rowKey]}
      data-index={props?.row.index}
      className={classNames(
        'ant-table-row',
        typeof props?.rowClassName === 'string'
          ? props?.rowClassName
          : props?.rowClassName
          ? props?.rowClassName(props?.row?.original, props?.row?.index)
          : '',
        props?.className,
      )}
      {...props?.virtualizeProps}
      {...props?.style}
      onClick={(event) => {
        props?.onRowClick &&
          props?.onRowClick?.(props?.row?.original, props?.row?.index);
      }}
    >
      {props?.children}
    </tr>
  );
};

export interface CommonCellProps {
  row: any;
  cell: any;

  enableMaxCharNumberEllipses?: boolean;
  maxEllipseCharNumber?: number;
  enableColumnResizing?: boolean;
}

const commonCellPropsEqual = (
  oldProps: CommonCellProps,
  newProps: CommonCellProps,
) => {
  return isEqual(oldProps?.cell?.getValue(), newProps?.cell?.getValue());
};

// 此处可选使用React.memo
export const CommonCell = (props: CommonCellProps) => {
  let enableTooltip = false;
  let mergedEllipseStyle = contentTextStyleProcessor(
    props?.cell.column.columnDef?.meta,
  );

  if (
    props?.enableMaxCharNumberEllipses === true &&
    props?.cell.getValue()?.length > (props?.maxEllipseCharNumber ?? 20)
  ) {
    enableTooltip = true;
  }

  if (props?.cell?.column?.getCanResize() === true) {
    enableTooltip = true;
  }

  const CellTooltipWrapper = enableTooltip === true ? Tooltip : EmptyWrapper;

  return (
    <>
      {flexRender(
        props?.cell.column.columnDef?.meta?.render(
          props?.cell.getValue(),
          props?.row.original,
          props?.row.index,
        ) ?? (
          <CellTooltipWrapper
            title={props?.cell.getValue()}
            placement={'topLeft'}
            getPopupContainer={getTooltipPopupContainer}
          >
            <span style={mergedEllipseStyle}>
              {props?.enableMaxCharNumberEllipses === true &&
              props?.cell.getValue()?.length >
                (props?.maxEllipseCharNumber ?? 20)
                ? `${props?.cell
                    .getValue()
                    ?.slice(0, props?.maxEllipseCharNumber ?? 20)}...`
                : props?.cell.getValue() ?? '-'}
            </span>
          </CellTooltipWrapper>
        ),
        props?.cell.getContext(),
      )}
    </>
  );
};

const cellWrapperPropsEqual = (oldProps: any, newProps: any) => {
  let headerWidth =
    document.getElementById(
      `th-${newProps?.cell?.column?.columnDef?.accessorKey}`,
    )?.offsetWidth ?? 0;

  return (
    isEqual(oldProps?.cell?.column, newProps?.cell?.column) &&
    isEqual(oldProps?.columnSize, newProps?.columnSize) &&
    isEqual(headerWidth, newProps?.columnSize)
  );
};

export const CellWrapper = (props: any) => {
  const cell = props?.cell;

  // console.log('column cell', cell);

  let cellVirtualizedStyles = {};
  if (props?.virtualized) {
    cellVirtualizedStyles = {
      width:
        props?.columnSize ||
        document
          .getElementById(`th-${cell?.column?.id}`)
          ?.getBoundingClientRect()?.width,
    };
  }

  let onCellProps: any = {};
  if (cell?.column?.columnDef?.meta?.onCell) {
    onCellProps = cell?.column?.columnDef?.meta?.onCell(
      props?.row?.original,
      props?.rowItem?.index,
    );
  }

  if (onCellProps?.colSpan === 0 || onCellProps?.rowSpan === 0) {
    return null;
  }

  let columnSizingStyle = {};
  let columnSizingContentItemStyle = {};
  if (
    cell?.column?.getCanResize() === true ||
    props?.enableColumnResizing === true
  ) {
    let headerWidth =
      document.getElementById(`th-${cell?.column?.columnDef?.accessorKey}`)
        ?.offsetWidth ?? 0;

    columnSizingStyle = {
      width: Math.max(props?.columnSize, headerWidth),
      maxWidth: Math.max(props?.columnSize, headerWidth),
      // minWidth: 'unset',
      minWidth: Math.max(props?.columnSize, headerWidth),
    };
  }

  let headerDraggingCellItem: any = {};
  if (props?.headerDragging === true) {
    headerDraggingCellItem = useDraggableTableAlongCell(cell);
  }

  return (
    <td
      ref={headerDraggingCellItem?.setNodeRef}
      className={classNames(
        'ant-table-cell',
        ...cellClassNameProcessor(cell.column, props?.tableInstance),
      )}
      style={{
        minWidth: props?.columnSize,
        maxWidth: props?.columnSize,
        textAlign: cell.column.columnDef.meta?.align ?? 'start',
        ...(cellStyleProcessor(cell.column, props?.tableInstance) as any),
        ...cellVirtualizedStyles,
        ...columnSizingStyle,
      }}
      {...onCellProps}
    >
      <CommonCell
        row={props?.row}
        cell={cell}
        enableMaxCharNumberEllipses={props?.enableMaxCharNumberEllipses}
        maxEllipseCharNumber={props?.maxEllipseCharNumber}
      />
    </td>
  );
};
