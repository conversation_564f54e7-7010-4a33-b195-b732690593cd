import isEqual from 'lodash/isEqual';
import React from 'react';
import { Tooltip } from 'antd';
import ColumnSorter, { toolTipLabel } from './column-sorter';
import classNames from 'classnames';
import { flexRender } from '@tanstack/react-table';
import ColumnFilter from './column-filter';
import {
  getTextAlign,
  cellStyleProcessor,
  cellClassNameProcessor,
  getMergeHeaderGroups,
  getTooltipPopupContainer,
} from '../utils';
import { GroupOutlined } from '@ant-design/icons';
import { useDraggableTableHeader } from './header-drag';
import {
  horizontalListSortingStrategy,
  SortableContext,
} from '@dnd-kit/sortable';

const headerPropsEqual = (oldProps: any, newProps: any) => {
  return (
    isEqual(oldProps?.header, newProps?.header) &&
    isEqual(oldProps?.size, newProps?.size)
  );
};

const tableHeaderPropsEqual = (oldProps: any, newProps: any) => {
  let oldHeaderGroup = oldProps?.tableInstance.getHeaderGroups();
  let newHeaderGroup = newProps?.tableInstance.getHeaderGroups();

  return isEqual(oldHeaderGroup, newHeaderGroup);
};

let HeaderRowWrapper = (props: any) => <>{props?.children}</>;

export const TableHeader = React.memo((props: any) => {
  if (props?.headerDragging === true) {
    HeaderRowWrapper = SortableContext;
  }

  return (
    <thead className={'ant-table-thead'} {...props?.virtualizationProps?.thead}>
      {getMergeHeaderGroups(props?.tableInstance.getHeaderGroups()).map(
        (headerGroup) => {
          return (
            <tr key={headerGroup.id}>
              <HeaderRowWrapper
                items={props?.columnOrder}
                strategy={horizontalListSortingStrategy}
              >
                {headerGroup.headers.map((header) => {
                  let columnResizingProps = {
                    onDoubleClick: () => header.column.resetSize(),
                    onMouseDown: header.getResizeHandler(),
                    onTouchStart: header.getResizeHandler(),
                    className: `resizer ltr ${
                      header.column.getIsResizing() ? 'isResizing' : ''
                    }`,
                    style: {},
                  };

                  return (
                    <HeaderItem
                      size={header?.getSize() ?? 'auto'}
                      header={header}
                      tableInstance={props?.tableInstance}
                      columnResizing={
                        header?.column?.getCanResize() === true
                          ? columnResizingProps
                          : {}
                      }
                      columnSizingState={props?.columnSizingState}
                      onCustomAggregableClick={props?.onCustomAggregableClick}
                    />
                  );
                })}
              </HeaderRowWrapper>
            </tr>
          );
        },
      )}
    </thead>
  );
});

export const HeaderItem = React.memo((props: any) => {
  const header = props?.header;

  // console.log('headerItem', header);

  const onColumnHeaderClick = () => {
    // 当且仅当sort时才触发
    if (header.column.columnDef.enableSorting) {
      // 判定sort 方向
      const currentSortOfColumn = header?.column?.getIsSorted();
      if (currentSortOfColumn === false) {
        // 是false 未排序 就升序
        header?.column?.toggleSorting(false);
      } else if (currentSortOfColumn === 'asc') {
        header?.column?.toggleSorting(true);
      } else if (currentSortOfColumn === 'desc') {
        header?.column?.clearSorting();
      }
    }
  };

  let Wrapper = (props: any) => <>{props?.children}</>;

  if (header.column.columnDef.enableSorting) {
    Wrapper = (props) => (
      <Tooltip
        placement={'top'}
        title={toolTipLabel[header?.column?.getIsSorted()?.toString()]}
      >
        <div
          className={'flex-row-center'}
          style={{
            width: '100%',
            maxWidth: '100%',
          }}
          onClick={onColumnHeaderClick}
        >
          {props?.children}
        </div>
      </Tooltip>
    );
  }

  let spanProps = {};
  if ((header?.colSpan ?? 1) > 1) {
    spanProps['colSpan'] = header?.colSpan;
  }

  if (header?.rowSpan) {
    spanProps['rowSpan'] = header?.rowSpan;
  }

  let columnSizingStyle = {};

  let columnSizingHeaderItemStyle = {};

  if (header?.column?.getCanResize() === true) {
    columnSizingStyle = {
      width: props?.size ?? 'auto',
      maxWidth: props?.size ?? 'auto',
      minWidth: 'unset',
    };
    columnSizingHeaderItemStyle = {
      userSelect: 'none',
      width: '100%',
      maxWidth: '100%',
    };
  } else {
    columnSizingStyle = {
      width: props?.size ?? 'auto',
      maxWidth: props?.size ?? 'auto',
      minWidth: props?.size ?? 'auto',
    };
  }

  let headerDraggingItem: any = {};
  if (props?.headerDragging === true) {
    headerDraggingItem = useDraggableTableHeader(header);
  }

  return (
    <th
      ref={headerDraggingItem?.setNodeRef}
      key={header.id}
      id={`th-${header?.id}`}
      {...spanProps}
      style={{
        minWidth: props?.size ?? 'auto',
        textAlign: getTextAlign(header?.column?.columnDef?.meta),
        ...(cellStyleProcessor(header?.column, props?.tableInstance) as any),
        ...columnSizingStyle,
        ...(headerDraggingItem?.style ?? {}),
      }}
      className={classNames(
        'ant-table-cell',
        header.column.columnDef.enableSorting
          ? 'ant-table-column-has-sorters'
          : '',
        ...cellClassNameProcessor(header?.column, props?.tableInstance),
      )}
    >
      {header.isPlaceholder ? null : (
        <div className={'header-item'} style={columnSizingHeaderItemStyle}>
          {props?.headerDragging === true && headerDraggingItem?.handler()}

          <div
            className={'flex-row-center'}
            style={{
              flex: 1,
              justifyContent: 'space-between',
              width: '100%',
              maxWidth: '100%',
            }}
          >
            <Wrapper>
              {flexRender(
                header?.column?.columnDef?.header,
                header.getContext(),
              )}
            </Wrapper>
            <div className={'flex-row-center'}>
              {header?.column?.columnDef?.meta?.isGroupable === true && (
                <Tooltip
                  title={'快速统计'}
                  getPopupContainer={getTooltipPopupContainer}
                >
                  <div
                    className={'combine-query-custom-grouping'}
                    onClick={() => {
                      props?.onCustomAggregableClick?.(
                        header?.column?.columnDef?.meta,
                      );
                    }}
                  >
                    <GroupOutlined />
                  </div>
                </Tooltip>
              )}
              {header?.column?.columnDef?.enableSorting && (
                <ColumnSorter column={header.column} />
              )}
            </div>
          </div>

          {header?.column?.columnDef?.enableColumnFilter && (
            <ColumnFilter column={header.column} />
          )}

          {header?.column?.getCanResize() && <div {...props?.columnResizing} />}
        </div>
      )}
    </th>
  );
}, headerPropsEqual);
