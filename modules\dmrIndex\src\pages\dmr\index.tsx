import React, {
  createRef,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import _, { debounce } from 'lodash';
import {
  Affix,
  Button,
  Card,
  ConfigProvider,
  Drawer,
  Form,
  FormInstance,
  Input,
  message,
  Modal,
  Radio,
  Spin,
  Tag,
  Tooltip,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import './index.less';
import 'react-grid-layout/css/styles.css';
import './components/right-container/precheck.less';
import { contentData } from '@/pages/dmr/fields/base';
import RightMenu, { TopMenuItem } from '@/pages/dmr/components/right-menu';
import InputSuffix from '@uni/grid/src/components/input-suffix';
import { DmrSearch } from '@/pages/dmr/components/dmr-search';
import { history, useRequest } from 'umi';
import { RespVO, ShortcutItem } from '@uni/commons/src/interfaces';
import {
  ClearOutlined,
  CommentOutlined,
  ContainerOutlined,
  EditOutlined,
  LoadingOutlined,
  SolutionOutlined,
  UpOutlined,
  UndoOutlined,
} from '@ant-design/icons';
import { useLocation } from 'umi';

import {
  ExceptionOutlined,
  TagOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  SaveOutlined,
  VerticalRightOutlined,
  VerticalLeftOutlined,
  PictureOutlined,
  PrinterOutlined,
  FullscreenOutlined,
} from '@ant-design/icons';

import qs from 'qs';
import {
  CardBundleCheck,
  CardBundleInfo,
  MedicalRecord,
} from '@/pages/dmr/network/interfaces';
import {
  dmrBundleThirdPartyCheckReq,
  getCardInfoV2,
  getDmrIndexLayoutConfig,
  getDmrMedicalRecordUrl,
  getDmrNotes,
  getDmrOperatorConfig,
  getDmrPreCheckModuleConfig,
} from '@/pages/dmr/network/get';
import {
  dmrExportBaseInfoReqV2,
  saveCardInfoV2,
} from '@/pages/dmr/network/save';
import PreCheckResult from '@/pages/dmr/components/right-container';
import {
  checkErrorOrNot,
  formValidation,
  getRequiredKeys,
} from '@/pages/dmr/rules';
import { useModel } from 'umi';
import merge from 'lodash/merge';
import mergeWith from 'lodash/mergeWith';
import flattenDeep from 'lodash/flattenDeep';
import { headerData } from '@/pages/dmr/fields/header';
import {
  deleteSpecialKeysProcessor,
  deleteSpecialKeysProcessorByPrefix,
  formKeyToNavigationId,
  noClickElementsClassName,
} from '@/pages/dmr/constants';
import { tableHotKeys } from '@uni/grid/src/common';
import { downloadFile } from '@uni/utils/src/download';
import {
  buildMessageOnSubmit,
  defaultValueToFormValueBeforeValidation,
  ignoreKeyStrokesInVisibilityChange,
  isInElementViewport,
} from '@/pages/dmr/utils';
import DmrQueryHeader from '@/pages/dmr/components/dmr-query-header';
import { useHotkeys } from 'react-hotkeys-hook';
import ShortcutsHelpModal from '@uni/grid/src/components/help-modal';
import { fees } from '@/pages/dmr/fields/fees';
import { auditValidation } from '@/pages/dmr/rules/audit';
import { PreCheckIcon, RecordIcon } from '@uni/components/src/custom-icon/icon';
import CommentModal from '@/pages/dmr/components/comment-modal';
import TraceModal from '@/pages/dmr/components/trace-modal';
import { ModalLoading } from '@uni/components/src';
import { generateLayout } from '@/utils/layouts';
import { GridItem } from '@uni/grid/src/common/item';
import {
  clearScrollingTitleTimeout,
  isEmptyValues,
  scrollTitle,
} from '@uni/utils/src/utils';
import { DmrProcessor } from '@/pages/dmr/processors/processors';
import { restrictToParentElement } from '@dnd-kit/modifiers';
import { configurableDataIndex } from '@/pages/configuration/properties/column';

import {
  Breakpoint,
  ColumnOptions,
  GridStack,
  GridStackEvent,
  GridStackEventHandlerCallback,
  GridStackNode,
} from '@uni/grid/src/core/gridstack';
import '@uni/grid/src/core/gridstack.css';
import '@uni/grid/src/style/index';
import {
  getSeparatorKey,
  onGridColumnChange,
} from '@uni/grid/src/core/custom-utils';
import { LAYOUT_COLUMNS } from '@uni/grid/src/common';
import { dynamicComponentsMap } from '@/utils/dynamicComponents';
import GridItemContext from '@uni/commons/src/grid-context';
import {
  defaultPageUpDownHandler,
  getDeletePressEventKey,
  getTransformHeightByElementId,
} from '@uni/grid/src/utils';
import cloneDeep from 'lodash/cloneDeep';
import { dmrThemeProcessor } from '@/pages/dmr/processors/others';
import ReactDOM from 'react-dom';
import DmrReadonly from '@/pages/doubleDeck/components/dmr-readonly';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import PreCheckbox from '@/pages/dmr/components/pre-examine/pre-checkbox';
import DmrRightCommentContainer from '@/pages/dmr/components/pre-examine/comment/index';

import ClickToFold from '@uni/commons/src/icon/ClickToFold';
import { getValueDependencies } from '@/pages/dmr/rules/value-dependency';
import { useUpdate, useUpdateEffect } from 'ahooks';
import uniq from 'lodash/uniq';
import {
  getDevicePixelRatio,
  getZoomLevel,
  matchMediaQueryString,
} from '@uni/utils/src/ratio';
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import IcdeDataPortal from '@/pages/dmr/components/icde-oper-portal/icde';
import OperDataPortal from '@/pages/dmr/components/icde-oper-portal/oper';
import ChargePortal from '@/pages/dmr/components/charge-portal';
import {
  babyHiddenFormItem,
  BabyModal,
  BabyOperationItemTitle,
} from './components/baby-modal/index';
import Diff from 'diff';
import {
  diffCardInEditing,
  diffCardInfoWithPreCard,
  keyIdentifier,
} from '@/pages/doubleDeck/components/diff';
import pick from 'lodash/pick';
import DmrChangeHistoryPortal from '@/pages/dmr/components/history-changes';
import { isKeyboardFriendlyDropdownVisible } from '@uni/grid/src/components/dmr-select/keyboard';
import dayjs from 'dayjs';
import { clearAllPreCommentClass } from '@/pages/dmr/components/pre-examine/comment/utils';
import SinginForTraceModal from './components/signin-trace-modal/index';

// *** 所有参数 函数 变量 内带Comment的 作用于 四川省人民医院 && 上海九院

const externalDmrConfig = (window as any).externalConfig?.['dmr'];
// 是否更新首页布局 【生产环境请严格设定为false (当且仅当首次部署设定为true，并于首页打开一次之后修改为false)
const updateLayout = externalDmrConfig?.['updateLayout'] ?? false;
// 是否为假的病案调阅
const fakeMedicalRecord = externalDmrConfig?.['fakeMedicalRecord'] ?? false;
// 保存时审核
const checkOnSave = externalDmrConfig?.['checkOnSave'] ?? false;
// 保存时签收
const signinOnSave = externalDmrConfig?.['signinOnSave'] ?? false;

// 首页input聚焦时是否选中全部（false: input聚焦时选中全部；true: input聚焦时不会选中全部）
const inputFocusNotSelectAll =
  externalDmrConfig?.['inputFocusNotSelectAll'] ?? false;
// 不修改可保存
const noEditSave = externalDmrConfig?.['noEditSave'] ?? false;
// 左右键切换格子
const leftRightKeySwitch = externalDmrConfig?.['leftRightKeySwitch'] ?? false;
// PageUp PageDown按键配置 【配置PageUp PageDown是否可用于切换模块（模块表示的是登记区域左侧导航部分）】
const dmrPageUpDownSwitchModule =
  externalDmrConfig?.['dmrPageUpDownSwitchModule'] ?? false;
// 时间输入框配置（true：按下enter即跳到下一格输入框）
const timescapeEnterSwitch =
  externalDmrConfig?.['timescapeEnterSwitch'] ?? false;
// 表格聚焦配置 （true：表格中任意一输入格被聚焦时会将其滚动到 登记部分的顶部）
const tableItemFocusTop = externalDmrConfig?.['tableItemFocusTop'] ?? false;
// 中心聚焦（当输入框聚焦时是否将其滚动到 登记部分 中心位置(目前这个flag不用了，请在生产环境严格设定为false)）
const centerInputActivate = externalDmrConfig?.['centerInputActivate'] ?? false;
// 点击编辑保持滚动到的位置 false: 点击编辑按钮会自动聚焦第一个可编辑的格子同时滚动到那里；设定为true时，会根据目前用户滚动到的位置来计算当前可视范围内第一个可编辑的格子并聚焦（不建议设为true，当且仅当有具体需求再设定为true）
const inEditRemainScrollPosition =
  externalDmrConfig?.['inEditRemainScrollPosition'] ?? false;
// 强制保存后开启 质控drawer
const forceDmrRightReview = externalDmrConfig?.['forceDmrRightReview'] ?? false;
// 首页登记/结算清单 自定义快捷键设置
const customShortcuts =
  (window as any).externalConfig?.['common']?.['customShortcuts'] ?? {};
// 触发滚动到中心的距离 默认值50：当某一格距离底部少于50像素时自动将其滚动到登记部分中心（不建议随意改动）
const forceScrollToCenterDistance =
  externalDmrConfig?.['forceScrollToCenterDistance'] ?? 0;
// 当质控审核无错误时自动跳转下一份
const saveAuditNoErrorAutoNext =
  externalDmrConfig?.['saveAuditNoErrorAutoNext'] ?? false;
// 退格键删除格子所有内容（没看到有地方使用）
const enableBackspaceDelete =
  externalDmrConfig?.['enableBackspaceDelete'] ?? false;
// 首页质控位置
const preCheckContainerPosition =
  externalDmrConfig?.['preCheckContainerPosition'] ?? 'right';
// 医生端 双栏***
const enableDoubleDeckDoctorEmr =
  externalDmrConfig?.['enableDoubleDeckDoctorEmr'] ?? false;
// 前置评审
const preCardExamineEnable =
  externalDmrConfig?.['preCardExamineEnable'] ?? false;
console.log(
  'preCardExamineEnable',
  window,
  (window as any).externalConfig,
  externalDmrConfig,
);
// 前置评审类型（checkbox/comment  comment 作用于 四川省人民）
const preCardExamineType = externalDmrConfig?.['preCardExamineType'] ?? '';

const dmrEditOperationKeys = ['EDIT', 'SAVE', 'CLEAR', 'SAVE_AFTER_CHECK'];

const errorKeyClick = true;

export const autoClick = false;

export const forceClickIds = ['department-transfer-container'];

export const ROW_HEIGHT = 42;
// 左侧导航栏
export const topMenuKeys: TopMenuItem[] = [
  {
    key: 'PatName',
    title: '基本',
    focusId: 'Input#PatName',
  },
  {
    key: 'diagnosisTable',
    title: '诊断',
    focusId: 'formItem#IcdeCode#0#IcdeSelect',
  },
  {
    key: 'OutHospital',
    title: '住院',
    focusId: 'formItem#OutType',
  },
  {
    key: 'operationTable',
    title: '手术',
    focusId: 'formItem#OperCode#0#OperSelect',
  },
  {
    key: 'pathologicalDiagnosisItem',
    title: '肿瘤',
    focusId: 'formItem#pathologicalDiagnosisCode#IcdeSelect',
  },
  {
    key: 'XX',
    title: '输血',
    focusId: 'formItem#XX#AutoSelect',
  },
  {
    key: 'Hbsag',
    title: '附页',
    focusId: 'formItem#DmrSelect#Hbsag',
  },
  {
    key: 'MedicalFee',
    title: '费用',
    focusId: 'formItem#MedfeeSumamt',
  },
];

const requireAndPreCheckRulesKeys = [
  'requiredKeys',
  'preCheckRules',
  'valueDependencies',
];

// table 用于数据的key
/**
 * 由于form表单中的table 会出现 刷新问题 因此设定一个 form item 用于 收集数据
 */
const hiddenFormItemTableKeys = [
  'diagnosis-table',
  'diagnosisTable',
  'operation-table',
  'operationTable',
  'pathological-diagnosis-table',
  'pathologicalDiagnosisTable',
  'icu-table',
  'icuTable',

  'tcm-diagnosis-table',
  'tcmDiagnosisTable',
  'departmentTransferTable',
  'department-transfer-table',
];

const formEditKey = 'formEdited';

const hiddenFormItemKeys = [
  // UniqueIds
  'IcdeOtpsUniqueId',
  'IcdeAdmsUniqueId',
  'IcdeDamgsUniqueId',
  'IcdePathosUniqueId',

  // 中医
  'TcmIcdeAdmsUniqueId',
  'TcmIcdeAdmsUniqueIcdeCode',

  'TcmIcdeOtpsUniqueId',
  'TcmIcdeOtpsMainUniqueId',

  'TcmIcdeOtpsUniqueIcdeCode',
  'TcmIcdeOtpsMainUniqueIcdeCode',
];

const noCenterElement = ['tr'];

const focusSelectAllClass = [
  'ant-input',
  'compact-date-container',
  'ant-select-selection-search-input',
];

// 浙妇保
// export const LAYOUT_COLUMNS = {
//   lg: 16,
//   md: 16,
//   sm: 14,
//   xs: 12,
//   xxs: 10,
// };

// 1920 * 1080
// 1520 1220

// 1680 * 1050
// 1280 980

// 1440 * 900
// 1040 740

// 1366 * 768
// 966 666

// 1280 * 800
// 880 580

// 1024 * 768
// 624 324

// lg: 1220
// md: 966
// sm: 740
// xs: 624 ? 580
// xxs: 324

// 现在只给 首页布局配置 使用了
// TODO 1280 * 800 双边展开已经很离谱了
export const BREAKPOINTS_LABEL = {
  lg: {
    label: '大屏',
    resolution: '1920*1080',
    width: '100%',
  },
  md: {
    label: '中屏',
    resolution: '1600 * 900 右侧推出',
    width: 918,
  },
  sm: {
    label: '小屏',
    resolution: '1366 * 768',
    width: 790,
  },
};

// const noRespondChangeFocusKeys = ['DiagnosisCode'];
const noRespondChangeFocusKeys = [];

let focusableElements = [
  'input:not([readonly]):not([disabled])',
  '[tabindex]:not([disabled]):not([tabindex="-1"]):not(tr)',
  "button[class*='ant-switch']",

  // 确保这个在最后
  '#department-transfer-container',
];
// 处理底部的费用部分（fields/fees.tsx）
const feeItemFormKeysForInitialize = () => {
  let feeDefaultValues = {};
  flattenDeep(fees?.slice())?.forEach((feeItem) => {
    feeItem?.data?.props?.items?.forEach((item) => {
      feeDefaultValues[item.formKey] = 0;
      if (item?.subFeeItems) {
        item?.subFeeItems?.map((subItem) => {
          feeDefaultValues[subItem.formKey] = 0;
        });
      }
    });
  });

  return feeDefaultValues;
};

const yLayout = {};
// 顶部的全部按钮（再跟用户的权限取交集成最终）
export const operations = (
  viewMode: boolean,
  currentDmrHisId: string,
  searchedHisIds: string[],
  extras?: any,
) => [
  // {
  //   key: 'CENTER',
  //   title: `${centerInputActivate ? '禁用' : '启用'}中心聚焦`,
  //   condition: location?.pathname?.indexOf('/fullscreen') !== -1,
  // },
  {
    key: 'DOCTOR_EMR',
    title: '显示医生端首页',
    disabled: false,
    icon: null,
    order: -1,
    enable: false,
  },
  {
    key: 'COMMENT',
    title: '批注',
    disabled: !!!currentDmrHisId,
    icon: <CommentOutlined />,
    order: 10,
  },

  {
    key: 'TRACE',
    title: '示踪',
    disabled: !!!currentDmrHisId,
    icon: <SolutionOutlined />,
    enable: true,
    order: 11,
  },
  {
    key: 'CLEAR',
    title: '清空',
    disabled: !!!currentDmrHisId,
    icon: <ClearOutlined />,
    enable: true,
    order: 0,
  },
  {
    key: 'EDIT',
    title: viewMode ? '编辑' : '取消编辑',
    disabled: !!!currentDmrHisId,
    icon: <EditOutlined />,
    enable: true,
    order: 1,
  },
  {
    key: 'SAVE',
    title: '保存',
    disabled: !!!currentDmrHisId,
    icon: <SaveOutlined />,
    enable: true,
    order: 3,
    color: '#216DF8',
  },
  {
    key: 'SAVE_AFTER_CHECK',
    title: '质控后保存',
    disabled: !!!currentDmrHisId,
    icon: <SaveOutlined />,
    enable: false,
    order: 3,
    color: '#216DF8',
  },
  {
    key: 'SIGNIN_FOR_TRACER',
    title: extras?.dmrSignInDate ? '撤销签收' : '签收',
    disabled: !!!currentDmrHisId,
    // icon: <SaveOutlined />,
    enable: true,
    order: 3.5,
    // color: '#216DF8',
  },
  {
    key: 'CHS',
    title: `结算清单`,
    disabled: !!!currentDmrHisId,
    icon: <RecordIcon />,
    enable: true,
    order: 9,
  },
  {
    key: 'CHSGROUP_AUDIT',
    title: '审核预分组',
    disabled: !!!currentDmrHisId,
    icon: <PreCheckIcon />,
    enable: true,
    order: 4,
    color: '#F4AC4D',
  },
  {
    key: 'PREVIOUS',
    title: '上一份',
    disabled: searchedHisIds?.length <= 0,
    icon: <VerticalRightOutlined />,
    enable: true,
    order: 4.5,
  },
  {
    key: 'NEXT',
    title: '下一份',
    disabled: searchedHisIds?.length <= 0,
    icon: <VerticalLeftOutlined />,
    enable: true,
    order: 5,
  },
  {
    key: 'MEDICAL_RECORDS',
    title: '调阅',
    disabled: !!!currentDmrHisId,
    icon: <PictureOutlined />,
    enable: true,
    order: 6,
  },
  {
    key: 'PRINT',
    title: '打印',
    disabled: !!!currentDmrHisId,
    icon: <PrinterOutlined />,
    enable: true,
    order: 8,
  },
  {
    key: 'FULLSCREEN',
    title: '全屏',
    icon: <FullscreenOutlined />,
    enable: true,
    order: 7,
  },
  {
    key: 'NORMAL_SCREEN',
    title: '恢复小屏幕',
    icon: <ClickToFold fill={'#eb5757'} />,
    enable: true,
    order: 7,
  },
  {
    key: 'RESET',
    title: '重置数据',
    icon: <UndoOutlined />,
    enable: false,
    order: 7,
  },
  {
    key: 'EMR_ICDE_OPER',
    title: '展示医生端诊断手术',
    icon: <ContainerOutlined />,
    enable: false,
    order: 7,
  },
  {
    key: 'DMR_ICDE_DATA',
    title: '展示首页诊断',
    enable: false,
    order: 7,
  },
  {
    key: 'DMR_OPER_DATA',
    title: '展示首页手术',
    enable: false,
    order: 7,
  },
  {
    key: 'CHARGE_FEE',
    title: '展示收费明细',
    enable: false,
    order: 7,
  },
  {
    key: 'THIRD_PARTY_CHECK',
    title: '第三方质控',
    disabled: !!!currentDmrHisId,
    enable: true,
    order: 7,
  },

  {
    key: 'EXTRA_BABY',
    title: (
      <BabyOperationItemTitle
        containerRef={extras?.babyTitleContainerRef}
        defaultCount={extras?.babyTitleDefaultCount}
      />
    ),
    disabled: !!!currentDmrHisId,
    enable: false,
    order: 10,
  },
  {
    key: 'CARD_DIFF',
    title:
      extras?.dmrReadonlyVisible === false ? '显示修改记录' : '隐藏修改记录',
    disabled: !!!currentDmrHisId,
    enable: false,
  },
  {
    key: 'DMR_CHANGE_HISTORY',
    title: '病案痕迹',
    disabled: !!!currentDmrHisId,
    enable: false,
  },
  {
    key: 'DMR_PRE_CARD_COMMENT',
    title: '首页批注',
    enable: false,
  },
];

// 主要就是费用那块了
export const defaultFieldValue = {
  // RYH_T: 0,
  // RYH_XS: 0,
  // RYH_FZ: 0,
  // RYQ_T: 0,
  // RYQ_XS: 0,
  // RYQ_FZ: 0,
  formEdited: false,
  ...feeItemFormKeysForInitialize(),
};
// 外部AOD方式使用首页登记时的入参内容
interface DmrProps {
  extra?: DmrExtraProps;
  dmrContainer?: any;

  [key: string]: any;
}

export interface DmrExtraProps {
  type:
    | 'REVISION'
    | 'HISTORICAL'
    | 'READONLY'
    | 'AlwaysOnDisplay'
    | 'QUALITY_EXAMINE';
  dmrRegistrationReadOnly: boolean; // 是否只读 用于覆盖config.js

  hisId?: string;
  rootContainerHeight?: number | string; // root-container height
  drawerTitle?: string; // 蒙版 标题
  leftContainerShow?: boolean; // 是否显示右侧搜索
  queryHeaderShow?: boolean; // 是否显示顶部搜索
  anchorShow?: boolean; // 是否显示快速导航
  qualityCheckShow?: boolean; // 是否显示质控
  preReview?: boolean; // 是否前端审核

  selectorWithNotValid?: boolean; // 选项是否包含 IsValid === false 项

  viewMode?: boolean;
  onCardBundleGetExtraProcess?: () => any;
  onOperationExtraProcess?: (type: string) => boolean;
  onFirstEditableInputFocus?: () => void;

  //用于历史数据
  saveInterfaceUrl?: string;

  isFullScreen?: boolean;

  commonContainerStyle?: React.CSSProperties;
  containerRef?: any;

  instantAudit?: boolean;

  dmrGridContainerRef?: any;
  detailCommentRef?: any;

  enableGridItemComment?: boolean;

  svgLinesContainerRef?: any;

  // standalone质控相关props
  enableStandaloneCheck?: boolean;
  standaloneCheckContainerRef?: any;
  openRightContainerWithoutCheck?: boolean;

  refresh?: boolean;

  // 强制显示 双栏
  forceDoubleDeckDoctorEmr?: boolean;

  // 外部 HisId 数组 上下一份
  operateNextPreviousExtraData?: boolean;
  nextPageHisIdsReplace?: boolean;
  extraSearchedHisIds?: string[];
  onExtraSearchedHisIdLastOne?: () => Promise<string[]>;
  onExtraSearchedHisIdChange?: (hisId: string) => void;

  // 评审check container 挂载点
  extraDmrContainerRef?: any;
  dmrExamineCheckContainerMountId?: string;

  // 首页批注
  dmrPreCardCommentRef?: any;
}
// 用于合并 form以及babyForm 所有值的函数
export const mergeFormFieldsValue = (...formInstances) => {
  let data = {};

  formInstances?.forEach((formInstance, index) => {
    merge(data, formInstance?.getFieldsValue());
    // 新生儿 删除问题 修复
    if (index === 1) {
      data['babies'] = formInstance?.getFieldsValue()?.['babies'];
    }
  });

  return data;
};

const DMR = (props: DmrProps) => {
  let headerGridInstance: GridStack = undefined;
  let contentGridInstance: GridStack = undefined;

  const tabHiddenPreviousActiveElementRef = useRef(null); // 存储标签页隐藏前的focus action元素
  const focusInEventTimestamp = useRef(0); // 存储焦点进入事件的时间戳，防止重复触发
  const containerScrollY = useRef(null); // 记录内容容器的滚动位置

  const dmrLeftSearchContainerRef = useRef(null); // 左侧 查询drawer 容器的引用

  const { globalState, setQiankunGlobalState, dmrAodProps, examineProps } =
    useModel('@@qiankunStateFromMaster');

  const dmrContainerRef = dmrAodProps?.dmrContainer ?? useRef(null); // 首页容器的引用 主要用于暴露给aod

  const gridContainerRef = useRef(null); // 网格容器的引用 主要用于暴露给aod

  const dmrPreCardCommentRef = useRef(null); // 首页批注引用

  console.log('dmrContainerRef', dmrContainerRef);

  React.useImperativeHandle(dmrContainerRef, () => {
    return {
      closeAllModal: () => {
        icdePortalContainerRef?.current?.showStatus({ status: false });
        operPortalContainerRef?.current?.showStatus({ status: false });
        chargePortalContainerRef?.current?.showStatus({ status: false });
        babyExtraContainerRef?.current?.showStatus({ status: false });
        // 还有定制的 notes
      },
      showDiff: (readonlyFormCardInfo: any) => {
        onDiffViewShow(readonlyFormCardInfo);
      },
    };
  });

  React.useImperativeHandle(dmrAodProps?.extra?.extraDmrContainerRef, () => {
    // 评审使用 获取RightContainer
    return {
      // TODO 暴露方法 具体方法再议
    };
  });

  // 如果存在 extra 值
  let extraProps: any = {};
  if (props?.extra) {
    extraProps = props?.extra;
    Object.keys(props?.extra).forEach((key) => {
      if (externalDmrConfig?.[key]) {
        // 如果是config  就按照props里面写的
        externalDmrConfig[key] = props?.extra[key];
      }
    });
  }

  if (!isEmptyValues(dmrAodProps)) {
    extraProps = {
      ...extraProps,
      ...(dmrAodProps ?? {}),
    };
  }

  React.useImperativeHandle(
    extraProps?.extra?.dmrGridContainerRef ?? gridContainerRef,
    () => {
      return {
        getGridStackInstance: () => {
          return {
            headerGridInstance: global['headerGridInstance'],
            contentGridInstance: global['contentGridInstance'],
          };
        },
        clientHeight:
          document?.getElementById('dmr-content-grid-layout')?.clientHeight ??
          0,
        getLayouts: () => layouts,
        // 拉起 批注
        showDmrPreComment: () => {
          setRightCommentContainerOpen(true);
        },
      };
    },
  );

  // 首页批注
  if (isEmptyValues(extraProps?.extra?.detailCommentRef)) {
    // comment 目前只有四川省人民医院使用
    if (preCardExamineType === 'comment' && preCardExamineEnable === true) {
      if (isEmptyValues(extraProps['extra'])) {
        extraProps['extra'] = {};
      }
      extraProps['extra']['enableGridItemComment'] = true;
      extraProps['extra']['dmrPreCardCommentRef'] = dmrPreCardCommentRef;
    }
  }

  // 是否只读模式 dmr read only 需要写里面 因为有地方会通过props传进来
  let dmrRegistrationReadOnly =
    extraProps?.extra?.dmrRegistrationReadOnly ??
    externalDmrConfig?.['dmrRegistrationReadOnly'] ??
    false;

  const [form] = Form.useForm();

  const [babyForm] = Form.useForm();
  const babyTitleContainerRef = React.useRef(null);

  const preCheckContainerRef = React.useRef(null);

  // location search
  const location = useLocation();

  const [layouts, setLayouts] = useState({});

  const [currentDmrHisId, setCurrentDmrHisId] = useState('');

  const [originDmrCardInfo, setOriginDmrCardInfo] =
    useState<CardBundleInfo>(null);

  const [registerStatusName, setRegisterStatusName] = useState('');
  const [dmrSignInDate, setDmrSignInDate] = useState(undefined);

  const [searchedHisIds, setSearchedHisIds] = useState<string[]>([]);
  // 左右drawer
  const [rightContainerOpen, setRightContainerOpen] = useState(false);
  const [leftContainerOpen, setLeftContainerOpen] = useState(false);

  const [rightCommentContainerOpen, setRightCommentContainerOpen] =
    useState(false);
  // 是否到达最后一份病案
  const [lastDmrRecordPageReached, setLastDmrRecordPageReached] =
    useState(false);
  // 是否为查看模式
  const [viewMode, setViewMode] = useState(
    extraProps?.extra?.['viewMode'] ?? true,
  );
  // 只读视图是否可见
  const [dmrReadonlyVisible, setDmrReadonlyVisible] = useState(false);

  const [formCardInfo, setFormCardInfo] = useState(undefined);

  const [breakpoint, setBreakpoint] = useState('lg');

  const [dmrOperatorConfig, setDmrOperatorConfig] = useState<any>({});
  const [dmrPreCheckModuleConfig, setDmrPreCheckModuleConfig] = useState<any>(
    {},
  );

  const [headerLayouts, setHeaderLayouts] = useState({});
  // 是否全屏显示
  const [isFullScreen, setIsFullScreen] = useState(
    extraProps?.extra?.isFullScreen ?? false,
  );
  // 顶部 搜索框 + 操作按钮List占用的高度
  const [rootOperationHeight, setRootOperationHeight] = useState<number>(0);
  // 用户对网页的缩放级别 packages\utils\src\ratio.ts
  const [zoomLevel, setZoomLevel] = useState(getZoomLevel());

  const [dmrProcessorInstance] = useState(() => new DmrProcessor());

  const compositionStatusRef = useRef<boolean>(false);

  const rightMenuContainerRef = useRef(null);

  const keyboardSwitchInputRef = useRef<boolean>(false);

  const dmrReadonlyContainerRef = useRef(null);
  // 操作上一个/下一个 病案时 额外刷新计数 用于获取新的病案明细内容（调接口）【只有在有extraProps的时候使用】
  const [
    operateNextPreviousExtraRefreshCount,
    setOperateNextPreviousExtraRefreshCount,
  ] = useState(0);

  // 各种弹出的可拖拽组件ref
  // 诊断（医生端）
  const icdePortalContainerRef = useRef(null);
  // 手术（首页 && 医生端都是）
  const operPortalContainerRef = useRef(null);
  // 收费明细
  const chargePortalContainerRef = useRef(null);
  // 首页痕迹（病案痕迹）
  const dmrChangeHistoryContainerRef = useRef(null);
  // 新生儿附页
  const babyExtraContainerRef = useRef(null);

  // aod fullscreen
  useEffect(() => {
    if (!isEmptyValues(dmrAodProps?.extra?.isFullScreen)) {
      setIsFullScreen(dmrAodProps?.extra?.isFullScreen);
    }
  }, [dmrAodProps]);

  useLayoutEffect(() => {
    if (rightContainerOpen === true) {
      // 关闭 右侧comment container
      setRightCommentContainerOpen(false);
    }
    setTimeout(() => {
      setRootOperationHeight(
        document.getElementById('dmr-root-operation-header')?.offsetHeight,
      );
    }, 300);
  }, [rightContainerOpen]);
  // preCardExamineEnable true && preCardExamineType 'checkbox' 时渲染的 ref
  // 病案错误上报 modules\dmrIndex\src\pages\dmr\components\pre-examine\pre-checkbox
  const preCheckboxContainerRef = useRef(null);

  const operationClickDebounce = (operationClickFunc: any) => {
    return debounce(operationClickFunc, 3000, {
      leading: true,
      trailing: false,
    });
  };
  // gridStack Breakpoint
  const breakpoints = (type: 'CONTENT' | 'HEADER'): Breakpoint[] => [
    {
      // 1920 * 1080
      c: LAYOUT_COLUMNS['lg'],
      w: 1508,
    },
    {
      // 1920 * 1080 右侧推出
      c: LAYOUT_COLUMNS['lg'],
      w: 1238,
    },
    {
      // 1600 * 900
      c: LAYOUT_COLUMNS['lg'],
      w: 1171,
    },
    {
      // 1600 * 900 右侧推出
      // layout: 'list' as any,
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['md'],
      w: 918,
    },
    {
      // 1440 * 800
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['md'],
      w: 1028,
    },
    {
      // 1440 * 800 推出
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['sm'],
      w: 790,
    },
    {
      // 1366 * 768
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['sm'],
      w: 954,
    },
  ];
  // ??
  const isDmrContentShow = () => {
    let dmrContainerElement = document.getElementById('dmr-form-container');
    const style = window.getComputedStyle(dmrContainerElement);
    const rect = dmrContainerElement.getBoundingClientRect();

    return (
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      rect.width > 0 &&
      rect.height > 0
    );
  };

  // shortcuts 拆分 revert部分shortcuts到原有 监听全局的逻辑
  const globalShortcuts: ShortcutItem[] = [
    {
      key: 'f1',
      callback: (event) => {
        Emitter.emit(EventConstant.DMR_HELP_MODAL);
      },
      enabled: true,
      description: '帮助',
    },
    {
      key: 'ctrl+e',
      callback: operationClickDebounce((event) => {
        if (currentDmrHisId) {
          onOperationItemClick('EDIT');
        }
      }),
      enabled: !!currentDmrHisId && dmrRegistrationReadOnly === false,
      description: '开始/取消 编辑',
    },
    {
      key: customShortcuts?.['SAVE'] || 'ctrl+s',
      callback: operationClickDebounce((event) => {
        if (currentDmrHisId) {
          onOperationItemClick('SAVE');
        }
      }),
      enabled:
        !!currentDmrHisId &&
        !!originDmrCardInfo &&
        dmrRegistrationReadOnly === false,
      description: '保存',
    },
    {
      key: 'f3',
      callback: operationClickDebounce((event) => {
        if (currentDmrHisId) {
          onOperationItemClick('CHSGROUP_AUDIT');
        }
      }),
      enabled: !!currentDmrHisId && !!originDmrCardInfo,
      description: '预分组 & 审核',
    },
    {
      key: 'f4',
      callback: operationClickDebounce((event) => {
        if (currentDmrHisId) {
          onOperationItemClick('NEXT');
        }
      }),
      enabled: !!currentDmrHisId && !!originDmrCardInfo,
      description: '下一份',
    },
    {
      key: 'f6',
      callback: operationClickDebounce((event) => {
        if (currentDmrHisId) {
          onOperationItemClick('MEDICAL_RECORDS');
        }
      }),
      enabled: !!currentDmrHisId && !!originDmrCardInfo,
      description: '调用电子病例',
    },
    {
      key: 'ctrl+p',
      callback: operationClickDebounce((event) => {
        if (currentDmrHisId) {
          onOperationItemClick('PRINT');
        }
      }),
      enabled: false,
      description: '打印',
    },
    {
      key: 'pagedown',
      callback: (event) => {
        if (event?.key === 'PageDown') {
          onPageUpDownPress(event, false);
        }
      },
      enabled: true,
      description: '向下滚动',
    },
    {
      key: 'pageup',
      callback: (event) => {
        if (event?.key === 'PageUp') {
          onPageUpDownPress(event, true);
        }
      },
      enabled: true,
      description: '向上滚动',
    },

    // 菜单跳转
    {
      key: 'ctrl+1...9',
      onlyForHint: true,
      description: '菜单项跳转',
    },
  ];

  const shortcuts: ShortcutItem[] = [
    {
      key: 'tab,ctrl+right,enter',
      callback: (event) => {
        if (compositionStatusRef.current === false) {
          Emitter.emit(EventConstant.DMR_TABLE_NEXT_KEY, {
            event: event,
            indexOffset: 1,
          });
        }
      },
      enabled: true,
      description: '下一项',
    },
    {
      key: 'right',
      callback: (event) => {
        if (compositionStatusRef.current === false) {
          Emitter.emit(EventConstant.DMR_TABLE_NEXT_KEY, {
            event: event,
            indexOffset: 1,
          });
        }
      },
      enabled: true,
      description: '下一项',
      // options: {
      //   enableOnContentEditable: false,
      // },
    },
    {
      key: 'shift+tab,ctrl+left',
      callback: (event) => {
        if (compositionStatusRef.current === false) {
          Emitter.emit(EventConstant.DMR_TABLE_NEXT_KEY, {
            event: event,
            indexOffset: -1,
          });
        }
      },
      enabled: true,
      description: '上一项',
    },
    {
      key: 'left',
      callback: (event) => {
        Emitter.emit(EventConstant.DMR_TABLE_PREVIOUS_KEY, {
          event: event,
          indexOffset: -1,
        });
      },
      enabled: true,
      description: '上一项',
      // options: {
      //   enableOnContentEditable: false,
      // },
    },
    {
      key: enableBackspaceDelete ? 'delete,backspace' : 'delete',
      callback: (event) => {
        onDeletePress(event);
      },
      enabled: true,
      description: '删除',
    },
    ...tableHotKeys,
  ];

  // hotkeys start
  // TODO 保存等快捷键

  const containerShortcutsRefs = globalShortcuts
    ?.filter((item) => item?.onlyForHint !== true)
    ?.map((item) => {
      return useHotkeys(item.key, item.callback, {
        enableOnFormTags: true,
        enabled: item?.enabled,
        preventDefault: !(item?.key === 'left' || item?.key === 'right'),
        enableOnContentEditable: true,
        ...(item?.options ?? {}),
      });
    });

  const shortcutsRefs = shortcuts
    ?.filter((item) => item?.onlyForHint !== true)
    ?.map((item) => {
      return useHotkeys(item.key, item.callback, {
        enableOnFormTags: true,
        enabled: item?.enabled,
        preventDefault: !(item?.key === 'left' || item?.key === 'right'),
        enableOnContentEditable: true,
        ...(item?.options ?? {}),
      });
    });

  // 菜单hotkeys
  const topMenuKeysRefs = topMenuKeys?.map((item, index) => {
    let key = `ctrl+${index + 1}`;
    return useHotkeys(
      key,
      () => {
        Emitter.emit(EventConstant.DMR_LEFT_MENU_CLICK, index);
      },
      {
        enableOnFormTags: true,
        enabled: true,
        preventDefault: true,
        enableOnContentEditable: true,
      },
    );
  });
  // hotkeys end

  // 滚动title
  useEffect(() => {
    // 表示 不是外部调用（不是通过AOD）
    if (formCardInfo && extraProps?.extra?.type === undefined) {
      // document.title
      let scrollingTitle = buildScrollTitle();
      if (scrollingTitle) {
        clearScrollingTitleTimeout();
        scrollTitle(scrollingTitle);
      }
    }
  }, [formCardInfo]);

  useEffect(() => {
    if (viewMode === true) {
      // 清空所有的 border-active
      unBorderActiveElements();
    }
  }, [viewMode]);

  // react 初始化
  useEffect(() => {
    // setLoading(true);
    Emitter.emit('DMR_LAYOUT_LOADING', true);
    //header Layout

    let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

    // 获取layout等多个渲染用配置
    dmrLayoutConfigReq(userInfo?.CliDepts?.at(0), userInfo?.HospCodes?.at(0), [
      'DmrOperatorsConfig',
      'DmrPreCheckModules',
      'DmrPreCheckRules',
      'DmrTheme',
    ]);

    // tab focus event
    Emitter.onMultiple(
      [EventConstant.DMR_TABLE_NEXT_KEY, EventConstant.DMR_TABLE_PREVIOUS_KEY],
      (data) => {
        keyboardSwitchInputRef.current = true;

        let event = data?.event;

        console.log('NEXT PREVIOUS EVENT', event);
        // TODO modal确认 不吃事件
        if ((event?.target as any)?.tagName?.toLowerCase() === 'button') {
          if (event?.target?.parentElement?.className?.includes('modal')) {
            event?.target?.click && event?.target?.click();
          }
          return;
        }

        // 当且仅当 左右按键键按下的时候
        if (inputFocusNotSelectAll === false && event?.ctrlKey === false) {
          if (event?.target?.tagName?.toLowerCase() === 'input') {
            let uniqueHosted = false;
            // checkbox的话
            if (event?.target?.type === 'checkbox') {
              uniqueHosted = true;
              if (
                (event.key === 'ArrowRight' || event.key === 'ArrowLeft') &&
                leftRightKeySwitch === false
              ) {
                return;
              }
            }

            // 如果是select 无条件跳转
            if (event?.target?.className?.indexOf('ant-select') > -1) {
              // 如果是icde oper select 不能强制跳转
              let icdeOperSelect =
                event?.target?.id?.toLowerCase()?.indexOf('icdeselect') > -1 ||
                event?.target?.id?.toLowerCase()?.indexOf('operselect') > -1;
              let peopleSelect =
                event?.target?.id?.toLowerCase()?.indexOf('employee') > -1;
              if (!icdeOperSelect && !peopleSelect) {
                uniqueHosted = true;
                if (
                  (event.key === 'ArrowRight' || event.key === 'ArrowLeft') &&
                  leftRightKeySwitch === false
                ) {
                  return;
                }
              }
            }

            // 如果是timescape 日期控件 单独做设定
            if (event?.target?.id?.toLowerCase()?.indexOf('timescape') > -1) {
              uniqueHosted = true;

              if (event?.key === 'Enter' && timescapeEnterSwitch === true) {
                // 当在timescape里面并且是Enter的时候 不能吃了Enter 需要跳格子
                uniqueHosted = false;
              }

              // 表示当前 还在日期控件内 需要日期控件接管
              if (
                event?.['timeScapeNextIndex'] !== undefined &&
                leftRightKeySwitch === false
              ) {
                return;
              }
            }

            if (uniqueHosted === false) {
              /**
               * 如果光标不在文本末尾，按右箭头不会跳到下一个字段而是在当前字段内移动光标
               * 如果光标不在文本开头，按左箭头不会跳到上一个字段而是在当前字段内移动光标
               * 如果整个文本被选中，则也不跳转字段
               */
              let selectionStart = event?.target?.selectionStart;
              let selectionEnd = event?.target?.selectionEnd;
              let selectionLength = event?.target?.value?.substring(
                selectionStart,
                selectionEnd,
              )?.length;

              if (event?.key === 'ArrowRight') {
                // 还没到最后了
                if (leftRightKeySwitch === false) {
                  return;
                }

                // 如果都被选中了 同时格子内容长度不能为0 就不能换格子
                if (
                  event?.target?.value?.length !== 0 &&
                  selectionLength === event?.target?.value?.length
                ) {
                  return;
                }

                if (selectionEnd !== event?.target?.value?.length) {
                  return;
                }
              }

              if (event?.key === 'ArrowLeft') {
                if (leftRightKeySwitch === false) {
                  return;
                }
                // 如果都被选中了 同时格子内容长度不能为0 就不能换格子
                if (
                  event?.target?.value?.length !== 0 &&
                  selectionLength === event?.target?.value?.length
                ) {
                  return;
                }

                // 还没到最前了
                if (selectionStart !== 0) {
                  return;
                }
              }
            }
          }
        }

        // 如果是 过敏药物 同时 过敏药物的 open = true 跳出
        if (event?.target?.id?.includes('AllergyDrugs')) {
          let listContainer = document
            ?.getElementById('formItem#AllergyDrugs#DmrSelect_list')
            ?.closest('div[class*=ant-select-dropdown-hidden]');
          // 这里的 === null 表示没有hidden 也就是open = true
          if (listContainer === null) {
            return;
          }
        }

        let needPropagation = true;

        // 如果是select 就还是再bubble上去 设定false
        // 对于大部分情况，阻止事件默认行为和传播，但对于select元素，允许事件继续传播，以保证下拉框的正常工作。
        if (
          Array.from(event?.target?.classList)?.filter((item: any) =>
            item?.includes('ant-select'),
          )?.length > 0
        ) {
          needPropagation = false;
        }

        if (needPropagation) {
          event?.stopPropagation && event?.stopPropagation();
        }

        event?.preventDefault && event?.preventDefault();

        // 核心导航光标逻辑
        /**
         * 在DOM中查询所有匹配的可聚焦元素
         * 找到当前元素在列表中的位置
         * 根据data.indexOffset（1表示向后，-1表示向前）计算下一个需要聚焦的元素
         * 对时间控件进行特殊处理
         * 确保不在模态框内进行导航
         * 将焦点设置到新元素上
         */
        requestAnimationFrame(() => {
          let componentId = (event.target as any)?.id;

          if (componentId) {
            // 不触发
            if (
              noRespondChangeFocusKeys.find(
                (item) => componentId.split('#')?.at(0) === item,
              )
            ) {
              return;
            }

            // 处理一下 过敏药物的事情
            let allergyValue = form.getFieldValue('Allergy');
            if (allergyValue === '2') {
              focusableElements.push(
                "input[id='formItem#AllergyDrugs#DmrSelect']",
              );
              focusableElements.push("input[id='formItem#AllergyDrugs']");
            }

            let focusableSelector = focusableElements.join(',');
            if (data?.indexOffset === -1) {
              focusableSelector = focusableElements
                ?.slice(0, focusableElements?.length - 1)
                .join(',');
            }

            let formInputElements = document
              .getElementById('dmr-form-container')
              // ?.getElementsByTagName("input");
              ?.querySelectorAll(focusableSelector);

            if (formInputElements && formInputElements?.length > 0) {
              let formInputs = [].slice.call(formInputElements);

              // 剔除 fieldset disabled 的
              formInputs = formInputs?.filter((inputItem) => {
                let closestFieldset = inputItem?.closest('fieldset');
                return closestFieldset?.disabled !== true;
              });

              let currentComponentIndex = formInputs.findIndex(
                (item) => item.id === componentId,
              );

              if (currentComponentIndex >= 0) {
                if (data?.indexOffset) {
                  let waitForFocusElement = formInputs?.at(
                    currentComponentIndex + data?.indexOffset,
                  );

                  // 如果是时间控件 需要找到下一个不是时间控件的控件 并且只有enter下
                  if (
                    componentId?.includes('TimeScape') &&
                    event?.key === 'Enter' &&
                    timescapeEnterSwitch === true
                  ) {
                    waitForFocusElement = formInputs
                      ?.slice(currentComponentIndex)
                      ?.find((item) => !item?.id?.includes('TimeScape'));
                  }

                  // 弹窗内部不响应
                  if (
                    waitForFocusElement?.parentElement?.className?.includes(
                      'ant-modal',
                    )
                  ) {
                    return;
                  }

                  if (waitForFocusElement) {
                    formInputs?.at(currentComponentIndex)?.blur();

                    if (forceClickIds?.includes(waitForFocusElement?.id)) {
                      waitForFocusElement?.click();
                      return;
                    } else {
                      if (
                        autoClick &&
                        waitForFocusElement?.click &&
                        Array.from(
                          waitForFocusElement?.classList ?? [],
                        )?.filter((classItem: string) =>
                          noClickElementsClassName?.includes(classItem),
                        ).length <= 0
                      ) {
                        waitForFocusElement?.click();
                        return;
                      }
                    }
                    waitForFocusElement?.focus();
                  }
                }
              }
            }
          }
        });
      },
    );

    Emitter.on(EventConstant.DMR_SEARCH_TABLE_HISIDS, (hisIds) => {
      setSearchedHisIds(hisIds);
    });

    Emitter.on(EventConstant.DMR_DRAWER_CHANGE, (status) => {
      setLeftContainerOpen(status);
    });

    Emitter.on(EventConstant.DMR_RECORD_LAST_PAGE, (status) => {
      setLastDmrRecordPageReached(status);
    });

    // device pixel ratio
    matchMedia(matchMediaQueryString).addEventListener('change', () => {
      setZoomLevel(getZoomLevel());
    });

    // 是否 离开窗口
    // 记录当前 活跃元素
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        // 强制焦点到body上 避免再次 滚动 同时启用点击 即悬停
        tabHiddenPreviousActiveElementRef.current = document.activeElement;
      } else {
        if (!isEmptyValues(tabHiddenPreviousActiveElementRef.current)) {
          // 回到原点 但是 不能滚动
          tabHiddenPreviousActiveElementRef.current?.focus?.();
          if (!isEmptyValues(containerScrollY.current)) {
            document?.getElementById('dmr-content-container')?.scrollTo({
              behavior: 'smooth',
              top: containerScrollY.current,
            });
          }
        }
      }
    });

    // 吃字符 DMRDEV-932 叫说 不行 还是要不吃字符 https://devops.aliyun.com/projex/bug/DMRDEV-932# 《从别的页面切换回登记页面会吞字符》
    // 判定为  A-Z 0-9 不吃掉
    document.addEventListener('keydown', (event) => {
      if (!isEmptyValues(tabHiddenPreviousActiveElementRef?.current)) {
        let currentActiveElement = document?.activeElement as any;
        let currentActiveElementId = currentActiveElement?.id;
        let tabVisibilityCheckActiveElementId =
          tabHiddenPreviousActiveElementRef?.current?.id;
        if (currentActiveElementId === tabVisibilityCheckActiveElementId) {
          if (ignoreKeyStrokesInVisibilityChange(event) === false) {
            currentActiveElement?.blur?.();
          }
          setTimeout(() => {
            currentActiveElement?.scrollIntoView?.({
              block: 'center',
              behavior: 'smooth',
            });
            currentActiveElement?.focus?.();
          }, 0);
        }

        tabHiddenPreviousActiveElementRef.current = null;
      }
    });

    return () => {
      Emitter.offMultiple([
        EventConstant.DMR_TABLE_NEXT_KEY,
        EventConstant.DMR_TABLE_PREVIOUS_KEY,
      ]);

      document.removeEventListener('keydown', () => {});

      Emitter.off(EventConstant.DMR_SEARCH_TABLE_HISIDS);
      Emitter.off(EventConstant.DMR_DRAWER_CHANGE);

      // make grid instance undefined
      headerGridInstance = undefined;
      contentGridInstance = undefined;

      matchMedia(matchMediaQueryString).removeEventListener('change', () => {});
      document.removeEventListener('visibilitychange', () => {});
    };
  }, []);

  useEffect(() => {
    window.addEventListener('beforeunload', function (e) {
      if (!isEmptyValues(extraProps?.extra?.type)) {
        // 表示从其他地方跳过来  关闭的时候不管了先
        return null;
      }

      // Cancel the event
      if (form.getFieldValue('formEdited') && currentDmrHisId) {
        e.preventDefault();
        let returnValue = '存在尚未保存的信息，确定要离开？';
        e.returnValue = returnValue;

        return returnValue;
      }

      return null;
    });

    return () => {
      // 移除提示
      window.removeEventListener('beforeunload', () => {});
    };
  }, [currentDmrHisId]);

  const onDmrHisIdUpdateGetInfo = () => {
    if (
      (isEmptyValues(location?.search) || isEmptyValues(layouts)) &&
      extraProps?.extra?.hisId === undefined
    ) {
      return;
    }

    let urlParam = qs.parse(location.search, {
      ignoreQueryPrefix: true,
    });

    let currentHisId =
      extraProps?.extra?.hisId ?? decodeURIComponent(urlParam?.hisId);

    let instantAudit =
      extraProps?.extra?.instantAudit ??
      decodeURIComponent(urlParam?.instantAudit);

    if (
      (currentHisId && currentHisId !== currentDmrHisId) ||
      (extraProps?.extra?.refresh === true && currentHisId === currentDmrHisId)
    ) {
      getDmrCardInfo(currentHisId, instantAudit);

      // 跳回最开始
      document.getElementById('dmr-content-container')?.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    onDmrHisIdUpdateGetInfo();

    if (!isEmptyValues(extraProps?.extra?.extraSearchedHisIds)) {
      setSearchedHisIds(extraProps?.extra?.extraSearchedHisIds);
    }

    if (extraProps?.extra?.forceDoubleDeckDoctorEmr === true) {
      setDmrReadonlyVisible(true);
    }
  }, [location?.search, layouts, extraProps?.extra]);

  useUpdateEffect(() => {
    onDmrHisIdUpdateGetInfo();
  }, [operateNextPreviousExtraRefreshCount]);

  useEffect(() => {
    // initialize yLayout
    console.log('initialize yLayout');

    if (!isEmptyValues(layouts)) {
      Object.keys(LAYOUT_COLUMNS)?.forEach((key) => {
        yLayout[key] = 0;
      });
      // TODO layouts改动 ？ 还是 breakpoint改动？

      dmrProcessorInstance.setLayouts(layouts);

      let gridOpts = {
        column: LAYOUT_COLUMNS['lg'],
        cellHeight: ROW_HEIGHT,
        sizeToContent: true,
        disableResize: true,
        disableDrag: true,
        marginTop: 0,
        marginLeft: 0,
        marginRight: 0,
        marginBottom: 0,
        staticGrid: true,
      };
      // 初始化 content gridStack
      if (contentGridInstance === undefined) {
        contentGridInstance = GridStack.init(
          {
            columnOpts: {
              columnMax: LAYOUT_COLUMNS['lg'],
              layout: 'move' as any,
              breakpoints: breakpoints('CONTENT')?.sort((a, b) => a.w - b.w),
            },
            ...gridOpts,
          },
          document.getElementById('dmr-content-grid-layout'),
        );

        contentGridInstance?.on('loaded', (name, callback) => {
          setTimeout(() => {
            Emitter.emit(EventConstant.DMR_MENU_POSITION_RECALCULATE);

            Emitter.emit(EventConstant.TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR);
            Emitter.emit(EventConstant.TABLE_LAYOUT_RESIZE_SEPARATOR);

            // 用于通知评论区 加载完了
            (global?.window as any)?.eventEmitter?.emit(
              'DMR_COMMENT_NOTIFY',
              false,
            );
            (global?.window as any)?.eventEmitter?.emit(
              'DMR_COMMENT_NOTIFY_STYLE',
              false,
            );
            (global?.window as any)?.eventEmitter?.emit(
              'DMR_COMMENT_NOTIFY_HEIGHT',
              false,
            );
          }, 800);
        });
        global['contentGridInstance'] = contentGridInstance;
      }
      // 初始化 header gridStack
      if (headerGridInstance === undefined) {
        global['headerGridInstance'] = headerGridInstance = GridStack.init(
          {
            columnOpts: {
              columnMax: LAYOUT_COLUMNS['lg'],
              layout: 'move' as any,
              breakpoints: breakpoints('HEADER')?.sort((a, b) => a.w - b.w),
            },
            ...gridOpts,
          },
          document.getElementById('dmr-header-grid-layout'),
        );
      }
    }
  }, [layouts]);

  useEffect(() => {
    if (contentGridInstance) {
      if (!isEmptyValues(layouts['lg'])) {
        contentGridInstance.batchUpdate();
        contentGridInstance.removeAll(false);
        let stackLayouts = [];

        let hideInDmrCount = 0;
        for (let item of layouts['lg']) {
          if (item?.data?.props?.hideInDmr === true) {
            hideInDmrCount++;
            continue;
          }
          contentGridInstance.makeWidget(
            `div[id="dmr-container-common"] #${item?.i}`,
          );
          stackLayouts.push({
            id: item?.i,
            x: item?.x,
            y: item?.y - hideInDmrCount,
            // sizeToContent: true,
            w: item?.w,
          });
        }
        contentGridInstance.load(stackLayouts);
        contentGridInstance.batchUpdate(false);
        Emitter.emit('DMR_LAYOUT_LOADING', false);
      }
    }
  }, [layouts, contentGridInstance]);

  useEffect(() => {
    if (headerGridInstance) {
      if (!isEmptyValues(headerLayouts['lg'])) {
        headerGridInstance.batchUpdate();
        headerGridInstance.removeAll(false);
        let stackLayouts = [];
        headerLayouts['lg']?.forEach((item) => {
          headerGridInstance.makeWidget(`#${item?.i}`);
          stackLayouts.push({
            id: item?.i,
            x: item?.x,
            y: item?.y,
            // sizeToContent: true,
            w: item?.w,
          });
        });

        headerGridInstance.load(stackLayouts);
        headerGridInstance.batchUpdate(false);
      }
    }
  }, [headerLayouts, headerGridInstance]);

  // required keys
  useEffect(() => {
    if (!isEmptyValues(layouts) && !isEmptyValues(headerLayouts)) {
      let requiredKeys = getRequiredKeys(layouts, headerLayouts);
      let valueDependencies = getValueDependencies(layouts, headerLayouts);

      form.setFieldValue('requiredKeys', requiredKeys);
    }
  }, [layouts, headerLayouts]);

  useEffect(() => {
    // 监听focus in
    document
      ?.getElementById('dmr-form-container')
      ?.addEventListener('focusin', (event) => {
        // 表示 从其他地方移动进来
        console.log('focus changed', event, document.activeElement);
        if (focusInEventTimestamp.current === event?.timeStamp) {
          event.preventDefault();
          return;
        }
        focusInEventTimestamp.current = event?.timeStamp;

        if (!isEmptyValues(tabHiddenPreviousActiveElementRef.current)) {
          // 当有previous的时候 不滚动
          return;
        }

        // Input 并且 readonly = true 不响应
        if (
          (event?.target as any)?.tagName?.toString()?.toLowerCase() === 'input'
        ) {
          if ((event?.target as any)?.readOnly === true) {
            let excludeIdKeywords = ['TimeScape'];
            let shouldSkip = true;

            for (let excludeIdKeyword of excludeIdKeywords) {
              if ((event?.target as any)?.id?.includes(excludeIdKeyword)) {
                shouldSkip = false;
              }
            }

            if (shouldSkip) {
              return;
            }
          }
        }
        let hasScrolled = false;
        if (centerInputActivate) {
          if (
            !noCenterElement?.includes(
              (event.target as any)?.tagName?.toString()?.toLowerCase(),
            ) &&
            (event.target as any).closest("td[class*='ant-table-cell']") !==
              null
          ) {
            scrollFocusedElementToCenter(document?.activeElement);
          }
        }

        if (tableItemFocusTop) {
          if (
            (event.target as any).closest("td[class*='ant-table-cell']") !==
            null
          ) {
            // 表示在表格内
            let containerScrollTop = document.getElementById(
              'dmr-content-container',
            )?.scrollTop;
            let tableContainerOffsetTop = (event.target as any).closest(
              "div[class~='grid-stack-item']",
            )?.offsetTop;

            const inViewPort = isInElementViewport(
              event?.target as any,
              document.getElementById('dmr-content-container'),
              forceScrollToCenterDistance,
            );

            // 跳过
            if (inViewPort && keyboardSwitchInputRef.current === true) {
              hasScrolled = true;
            }

            let inTableIndexString = (event.target as any)?.id
              ?.split('#')
              ?.at(2);
            if (!isEmptyValues(inTableIndexString)) {
              let inTableIndex = parseInt(inTableIndexString);
              if (inTableIndex > 10) {
                (event?.target as any)?.scrollIntoView({
                  block: 'center',
                  inline: 'center',
                  behavior: 'smooth',
                });
                hasScrolled = true;
              } else {
                if (containerScrollTop != tableContainerOffsetTop) {
                  document.getElementById('dmr-content-container')?.scrollTo({
                    top: tableContainerOffsetTop,
                    behavior: 'smooth',
                  });
                  hasScrolled = true;
                }
              }
            }
          }
        }

        // 当前这个item 距离底部多少的时候强制 滚动到屏幕正中间 防止看不见 一般来说是100 可设置0， 0 的话不会自动滚动
        if (forceScrollToCenterDistance !== 0) {
          if (!isEmptyValues(tabHiddenPreviousActiveElementRef.current)) {
            // 当有prevoius的时候 不滚动
            tabHiddenPreviousActiveElementRef.current = null;
            return;
          }
          const inViewPort = isInElementViewport(
            event?.target as any,
            document.getElementById('dmr-content-container'),
            forceScrollToCenterDistance,
          );
          if (!inViewPort && !hasScrolled) {
            (event?.target as any)?.scrollIntoView({
              block: 'center',
              inline: 'center',
              behavior: 'smooth',
            });
          }
        }

        borderActiveInputs();

        if (inputFocusNotSelectAll === false) {
          let classList = (event.target as any)?.classList;
          let selectAll = false;
          for (let classItem of classList) {
            if (focusSelectAllClass?.includes(classItem)) {
              selectAll = true;
              break;
            }
          }

          if (selectAll) {
            (event.target as any)?.select();
          }
        }

        keyboardSwitchInputRef.current = false;
      });

    document
      ?.getElementById('dmr-form-container')
      ?.addEventListener('drop', (event) => {
        // 如果是 输入框 禁止文案拖放操作
        if ((event?.target as any)?.tagName.toLowerCase() === 'input') {
          event.dataTransfer.dropEffect = 'none';
          event.stopPropagation();
          event.preventDefault();
        }
      });

    return () => {
      document
        ?.getElementById('dmr-form-container')
        ?.removeEventListener('focusin', () => {});

      document
        ?.getElementById('dmr-form-container')
        ?.removeEventListener('drop', () => {});
    };
  }, [document?.getElementById('dmr-form-container')]);

  // 构建 document scrollTitle
  const buildScrollTitle = () => {
    let scrollingTitles = [];

    if (formCardInfo?.PatName) {
      scrollingTitles.push(`姓名：${formCardInfo?.PatName}`);
    }

    if (formCardInfo?.PatAdmNo) {
      scrollingTitles.push(`住院号：${formCardInfo?.PatAdmNo}`);
    }

    if (formCardInfo?.PatNo) {
      scrollingTitles.push(`病案号：${formCardInfo?.PatNo}`);
    }

    return scrollingTitles.join('，') + '    ';
  };

  /** 接口start */

  const getDmrCardInfo = async (hisId: string, instantAudit?: boolean) => {
    if (hisId) {
      // setDmrCardInfoLoading(true);
      Emitter.emit('MODAL_LOADING_STATUS', true);

      // 关闭 弹窗icde oper charge
      icdePortalContainerRef?.current?.showStatus({ status: false });
      operPortalContainerRef?.current?.showStatus({ status: false });
      chargePortalContainerRef?.current?.showStatus({ status: false });
      setRightCommentContainerOpen(false);
      setRightContainerOpen(false);

      // 清空一下先
      if (!isEmptyValues(dmrPreCardCommentRef)) {
        dmrPreCardCommentRef?.current?.clearComment();
      }
      // 清空一下颜色标记
      clearAllPreCommentClass();

      // 批注
      // let dmrNotesResp = await getDmrNotes(hisId);

      let { formFieldValue, cardBundleInfo } = await getCardInfoV2(
        hisId,
        dmrProcessorInstance,
      );

      let formCardInfo = mergeWith(
        {},
        defaultFieldValue,
        formFieldValue,
        (objValue, srcValue, key) => {
          if (srcValue !== null && srcValue !== undefined) {
            return srcValue;
          }

          return objValue;
        },
      );
      // 重置viewMode
      let viewModeByRegisterStatus =
        cardBundleInfo?.CardFlat?.RegisterStatus === '1' ? false : true;
      if (dmrRegistrationReadOnly === false) {
        setViewMode(viewModeByRegisterStatus);
      } else {
        // 当未只读的时候只能看 不能跳到编辑
        viewModeByRegisterStatus = true;
        setViewMode(true);
      }

      if (extraProps?.extra?.onCardBundleGetExtraProcess) {
        let extraProcessResult =
          extraProps?.extra?.onCardBundleGetExtraProcess();
        viewModeByRegisterStatus =
          extraProcessResult?.['viewModeByRegisterStatus'] ??
          viewModeByRegisterStatus;
        setViewMode(viewModeByRegisterStatus);
      }

      if (viewModeByRegisterStatus === false) {
        if (extraProps?.extra?.onFirstEditableInputFocus) {
          extraProps?.extra?.onFirstEditableInputFocus();
        } else {
          setTimeout(() => {
            // 第一个可以编辑的项目
            const firstEditableInput = document
              .getElementById('dmr-content-container')
              ?.querySelector('input:not([disabled])');
            if (firstEditableInput) {
              (firstEditableInput as any)?.focus();
              (firstEditableInput as any)?.click();
            }
          }, 300);
        }
      }

      // form.resetFields();
      // reset form table fields
      resetTableData();
      resetFormItemData();
      unBorderAllErrorElements();
      removeDiffItemOnChangeHisId();
      form.setFieldValue(formEditKey, false);

      form.setFieldsValue(formCardInfo);
      setFormCardInfo(formCardInfo);
      setCurrentDmrHisId(hisId);
      setOriginDmrCardInfo(cardBundleInfo);
      // 状态
      setRegisterStatusName(formFieldValue['RegisterStatusName']);
      setDmrSignInDate(formFieldValue['DmrSignInDate']);

      // 新生儿附页
      babyForm.resetFields();
      babyHiddenFormItem?.forEach((key) => {
        babyForm.setFieldValue(key, formFieldValue?.[key]);
      });

      extraProps?.extra?.onExtraSearchedHisIdChange &&
        extraProps?.extra?.onExtraSearchedHisIdChange(hisId);

      if (instantAudit?.toString() === 'true') {
        setTimeout(() => {
          Emitter.emit(EventConstant.DMR_CHSGROUP_AUDIT, {
            hisId: hisId,
            originDmrCardInfo: cardBundleInfo,
            formFieldsValue: formCardInfo,
          });
        }, 0);
        if (preCheckContainerPosition === 'bottom') {
          document.getElementById('content').style.overflowY = 'hidden';
        }
        setRightContainerOpen(true);
        setRightCommentContainerOpen(false);
      } else {
        // 清除右侧
        Emitter.emit(EventConstant.DMR_CARD_DATA_RESET);
      }

      if (extraProps?.extra?.openRightContainerWithoutCheck === true) {
        setRightContainerOpen(true);
      }

      // setDmrCardInfoLoading(false);
      Emitter.emit('MODAL_LOADING_STATUS', false);

      // 定位到第一个能填写的地方
      // document?.getElementById('formItem#InTimes#RestrictInputNumber')?.focus();
      // document?.getElementById('formItem#InTimes#RestrictInputNumber')?.click();
    } else {
      message.error('HisId缺失，请检查');
    }
  };

  // 获取Layouts(header + content) && OperatorsConfig && PreCheckModules && PreCheckRules && theme
  const { loading: dmrLayoutsGetLoading, run: dmrLayoutConfigReq } = useRequest(
    (cliDeptsCode, hospCode, modules) => {
      return getDmrIndexLayoutConfig(cliDeptsCode, hospCode, modules);
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          // header
          let headerLayouts = generateLayout(
            response?.data?.DmrHeaderLayout || {},
            updateLayout,
            headerData,
            'DmrHeaderLayout',
          );

          setHeaderLayouts(headerLayouts);
          // content
          let layouts = generateLayout(
            response?.data?.DmrLayout || {},
            updateLayout,
            contentData,
            'DmrLayout',
          );

          setLayouts(layouts);

          // 目前master没用到这块代码 没有 接受emitter事件的地方
          Emitter.emit(EventConstant.DMR_DOUBLE_DECK_LAYOUT, {
            headerLayouts: headerLayouts,
            layouts: layouts,
          });

          // Emitter.emit("DMR_LOADING", false);

          setDmrOperatorConfig(response?.data?.DmrOperatorsConfig ?? {});
          setDmrPreCheckModuleConfig(response?.data?.DmrPreCheckModules ?? {});
          // theme
          dmrThemeProcessor(response?.data?.DmrTheme ?? {});

          // DmrPreCheckRules
          form.setFieldValue(
            'preCheckRules',
            response?.data?.DmrPreCheckRules ?? {},
          );
        } else {
          setLayouts({});
          setHeaderLayouts({});
          setDmrOperatorConfig({});
          setDmrPreCheckModuleConfig({});
        }
      },
    },
  );

  /** 接口 end */

  // 切换病案时的拦截器 用于判定编辑模式下 是否存在未保存的东西 并且被跳转了
  const onDmrCardChangeInterceptor = (label?: string) => {
    return new Promise<boolean>((resolve, reject) => {
      // 表示的是 当使用的是AOD type的时候 就直接跳出
      if (extraProps?.extra?.type === 'AlwaysOnDisplay') {
        resolve(false);
      }

      if (!viewMode) {
        // 有操作过的时候
        if (form.getFieldValue('formEdited')) {
          Modal.confirm({
            title: `有未保存的更改，确定要${label ?? '切换病案'}？`,
            content: `${label ?? '切换病案'}会舍弃当前病案修改的内容`,
            onOk: () => {
              resolve(false);
            },
            onCancel: () => {
              reject();
            },
            getContainer: () =>
              document?.getElementById('dmr-base-container') ?? document.body,
          });
        } else {
          resolve(false);
        }
      } else {
        resolve(false);
      }
    });
  };

  const onFinish = (values: any) => {
    console.log('Success:', values);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };
  // 标记表单错误项并高亮显示
  const borderErrorInputs = (errorKeys: string[], navigate?: boolean) => {
    // reset border error
    let allErrorElements = document
      .getElementById('dmr-form-container')
      ?.getElementsByClassName('border-error');
    let errorElements = [].slice.call(allErrorElements);
    errorElements?.forEach((errorElement) => {
      errorElement?.classList?.remove('border-error');
    });

    if (errorKeys?.length === 0) {
      return;
    }

    errorKeys?.forEach((key, index) => {
      if (key.includes('Table')) {
        let keys = key.split('~');
        let errorTableCell = document?.querySelector(
          `tr[data-index='${keys?.at(2)}'] td[rowindex='${keys?.at(
            2,
          )}'][dataindex='${keys?.at(1)}']`,
        );
        if (!isEmptyValues(errorTableCell)) {
          errorElementsClick(errorTableCell);
        }
      } else {
        if (key === 'RH') {
          // RH 特殊处理
          let errorElement = document?.getElementById(`formItem#Rh`);
          if (errorElement) {
            errorElement
              ?.closest('.form-content-item-container')
              ?.classList?.remove('border-active');
            errorElement
              ?.closest('.form-content-item-container')
              ?.classList?.add('border-error');

            if (index === 0) {
              errorElementsClick(errorElement);
            }
          }
        } else {
          let errorElement = document?.getElementById(`formItem#${key}`);
          if (errorElement) {
            errorElement
              ?.closest('.form-content-item-container')
              ?.classList?.remove('border-active');
            errorElement
              ?.closest('.form-content-item-container')
              ?.classList?.add('border-error');

            if (index === 0) {
              errorElementsClick(errorElement);
            }
          }
        }
      }
    });

    if (navigate) {
      let firstErrorKey = errorKeys?.at(0);
      let errorKeyNavigateId = firstErrorKey;

      let errorKeyReflectKey = Object.keys(formKeyToNavigationId)?.find((key) =>
        new RegExp(key).test(firstErrorKey),
      );
      if (errorKeyReflectKey) {
        errorKeyNavigateId = formKeyToNavigationId[errorKeyReflectKey];
      }

      if (errorKeyNavigateId) {
        let transformHeight = getTransformHeightByElementId(errorKeyNavigateId);
        if (transformHeight) {
          document.getElementById('dmr-content-container')?.scrollTo({
            top: transformHeight,
            behavior: 'smooth',
          });
        }
      }
    }
  };
  // 标记表单内 的 表格内 的错误项并高亮显示
  const borderErrorInputsInTable = (
    tableErrorKeyItem: any,
    navigate?: boolean,
  ) => {
    if (isEmptyValues(tableErrorKeyItem)) {
      return;
    }

    // 剔除所有的 包含 table-check-error-highlight-item
    let hasCheckErrorHighlightItems = document
      .getElementById('dmr-form-container')
      ?.getElementsByClassName('table-check-error-highlight-item');

    [].slice.call(hasCheckErrorHighlightItems).forEach((item) => {
      item?.classList.remove('table-check-error-highlight-item');
    });

    let keys = tableErrorKeyItem?.key.split('~');
    let errorTableCell = document?.querySelector(
      `tr[data-index='${keys?.at(2)}'] td[rowindex='${keys?.at(
        2,
      )}'][dataindex='${keys?.at(1)}']`,
    );
    if (!isEmptyValues(errorTableCell)) {
      errorElementsClick(errorTableCell);
    }

    let originValues = tableErrorKeyItem?.OriginalInputValue?.split('|');

    // 标记行 根据 OriginalInputValue 标记 限定rulecode
    if (tableErrorKeyItem?.key?.includes('diagnosisTable')) {
      let tableData = form?.getFieldValue('diagnosis-table');
      const icdeTableItem = document.getElementById('diagnosisTable');
      tableData?.forEach((item, index) => {
        if (originValues?.includes(item?.IcdeCode)) {
          // 标记 行
          let waitForHighlightLineItem = icdeTableItem?.querySelector(
            `tbody > tr:nth-child(${index + 1})`,
          );
          if (!isEmptyValues(waitForHighlightLineItem)) {
            waitForHighlightLineItem?.classList?.add(
              'table-check-error-highlight-item',
            );
          }
        }
      });
    }

    if (tableErrorKeyItem?.key?.includes('operationTable')) {
      let tableData = form?.getFieldValue('operation-table');
      const operationTableItem = document.getElementById('operationTable');
      tableData?.forEach((item, index) => {
        if (originValues?.includes(item?.OperCode)) {
          // 标记 行
          let waitForHighlightLineItem = operationTableItem?.querySelector(
            `tbody > tr:nth-child(${index + 1})`,
          );
          if (!isEmptyValues(waitForHighlightLineItem)) {
            waitForHighlightLineItem?.classList?.add(
              'table-check-error-highlight-item',
            );
          }
        }
      });
    }

    if (navigate) {
      const errorKeyNavigateId = keys?.at(0);
      if (errorKeyNavigateId) {
        let transformHeight = getTransformHeightByElementId(errorKeyNavigateId);
        if (transformHeight) {
          document.getElementById('dmr-content-container')?.scrollTo({
            top: transformHeight,
            behavior: 'smooth',
          });
        }
      }
    }
  };
  // 点击错误元素时的处理【该错误 跟上面的borderError 一致】
  // 直接给其对应input的焦点
  const errorElementsClick = (element: any) => {
    if (errorKeyClick) {
      element?.getElementsByTagName('input')?.[0]?.focus();
    }
  };
  // 为当前活动的输入元素添加边框高亮（或者叫只取消error，确保默认focus样式生效）
  const borderActiveInputs = () => {
    // reset border active
    // unBorderActiveElements();
    unBorderErrorElements(document?.activeElement);
    // borderActiveElements(document?.activeElement, true);
  };
  // 将聚焦的元素滚动到视图中心
  const scrollFocusedElementToCenter = (element: Element) => {
    // TODO 临时解决方案  checkbox switch不做 居中
    if (!(element.id.includes('IsMain') || element.id.includes('IsReported'))) {
      element?.scrollIntoView({
        block: 'center',
        inline: 'center',
        // behavior: 'smooth'
      });
    }
  };
  // 为活动元素添加边框 （先留着，默认有一个统一样式）
  const borderActiveElements = (element: any, border?: boolean) => {
    let closestElement = element?.closest('.form-content-item-container');
    if (closestElement) {
      if (border) {
        closestElement?.classList?.add('border-active');

        // active table line item
        // 有且仅有一个能border
        let closestTBodyElement = element?.closest('tbody');
        if (closestTBodyElement) {
          // 删除所有的 tr-border-active
          let allTrElementsUnderTbody = closestTBodyElement?.querySelectorAll(
            'tr[class*=ant-table-row]',
          );
          let trElementsList = [].slice.call(allTrElementsUnderTbody);
          if (trElementsList?.length > 0) {
            trElementsList?.forEach((trElement) => {
              trElement?.classList?.remove('tr-border-active');
            });
          }
        }

        let closestTrElement = element?.closest('tr[class*=ant-table-row]');
        if (closestTrElement) {
          closestTrElement?.classList?.add('tr-border-active');
        }
      } else {
        closestElement?.classList?.remove('border-active');

        // 有且仅有一个能border
        let closestTrElement = element?.closest('tr[class*=ant-table-row]');
        if (closestTrElement) {
          closestTrElement?.classList?.remove('tr-border-active');
        }
      }
    }
  };
  // 移除所有活动元素的边框高亮
  const unBorderActiveElements = () => {
    console.log('unBorderActiveElements');
    let activeElements = document.querySelectorAll('.border-active');
    if (activeElements && activeElements?.length > 0) {
      let activeElementsList = [].slice.call(activeElements);
      activeElementsList?.forEach((elementItem) => {
        elementItem?.classList?.remove('border-active');

        let closestTrElement = elementItem?.closest('tr[class*=ant-table-row]');
        if (closestTrElement) {
          closestTrElement?.classList?.remove('tr-border-active');
        }
      });
    }
  };
  // 移除特定元素的错误边框
  const unBorderErrorElements = (focusElement) => {
    let errorClassNameKey = 'div[class*="border-error"]';
    let errorElement = focusElement?.closest(errorClassNameKey);
    if (errorElement) {
      errorElement?.classList?.remove('border-error');
    }
  };
  // 移除所有错误元素的边框
  const unBorderAllErrorElements = () => {
    let errorClassNameKey = 'div[class*="border-error"]';
    let errorElements = document?.querySelectorAll(errorClassNameKey);
    if (errorElements && errorElements?.length > 0) {
      let errorElementsList = [].slice.call(errorElements);
      errorElementsList?.forEach((errorElement) => {
        errorElement?.classList?.remove('border-error');
      });
    }
  };
  // 切换病历时 移除差异对比的视觉标记
  const removeDiffItemOnChangeHisId = () => {
    document
      .getElementById('dmr-main-container')
      ?.querySelectorAll('.dmr-diff-container')
      ?.forEach((item) => {
        item?.classList?.remove('dmr-diff-container');
      });

    document
      .getElementById('dmr-readonly-container')
      ?.querySelectorAll('.readonly-diff-container')
      ?.forEach((item) => {
        item?.classList?.remove('readonly-diff-container');
      });
  };
  // 找到container顶部的 第一个可编辑输入框并聚焦
  const mostTopInScreenInput = () => {
    let scrollTop = document.getElementById('dmr-content-container').scrollTop;

    let contentItemElements = document.querySelectorAll(
      '#dmr-content-grid-layout > div',
    );
    if (contentItemElements?.length > 0) {
      let contentItems = [].slice.call(contentItemElements);

      for (let contentItem of contentItems) {
        let offsetScrollPosition = scrollTop - (contentItem?.offsetTop ?? 0);
        if (offsetScrollPosition <= ROW_HEIGHT / 2) {
          const firstEditableInput = contentItem?.querySelector(
            'input:not([disabled])',
          );
          if (firstEditableInput) {
            setTimeout(() => {
              (firstEditableInput as any)?.focus();
              (firstEditableInput as any)?.click();
            }, 0);
            break;
          }
        }
      }
    }
  };
  // 显示差异对比（双栏用）
  const onDiffViewShow = (
    readonlyCardInfo: any,
    currentDmrFormValues?: any,
  ) => {
    // 即时对比
    let currentDmrCardInfo = currentDmrFormValues ?? form?.getFieldsValue(true);

    diffCardInfoWithPreCard(currentDmrCardInfo, readonlyCardInfo);
  };

  const onOperationItemClick = async (key, forceUseSave?: boolean) => {
    // 当 存在被禁止的 按钮被触发 跳出去 防止快捷键触发

    // 由于新增了sava after check 这里要判定是不是SAVE after Check key
    let operationKey = key;
    if (key === 'SAVE' && forceUseSave !== true) {
      let saveAfterCheckItem = operations(
        viewMode,
        currentDmrHisId,
        searchedHisIds,
        {
          dmrSignInDate: dmrSignInDate,
          registerStatusName: registerStatusName,
        },
      )?.find((operationItem) => operationItem?.key === 'SAVE_AFTER_CHECK');
      let saveAfterCheckEnable = canOperatorEnabled(saveAfterCheckItem);
      if (saveAfterCheckEnable === true) {
        operationKey = 'SAVE_AFTER_CHECK';
      }
    }

    if (forceUseSave !== true) {
      let item = operations(viewMode, currentDmrHisId, searchedHisIds, {
        dmrSignInDate: dmrSignInDate,
        registerStatusName: registerStatusName,
      })?.find((operationItem) => operationItem?.key === operationKey);
      if (isEmptyValues(item) || !canOperatorEnabled(item)) {
        return;
      }
    }

    switch (operationKey) {
      case 'DOCTOR_EMR':
        setDmrReadonlyVisible(!dmrReadonlyVisible);
        break;
      case 'COMMENT':
        Emitter.emit(EventConstant.DMR_COMMENT_MODAL, true);
        break;
      case 'TRACE':
        // 00276182+2020-01-08
        Emitter.emit(EventConstant.DMR_TRACE_MODAL, true);
        break;
      case 'EDIT':
        if (dmrRegistrationReadOnly === true) {
          return;
        }

        let currentViewMode = !viewMode;
        if (!currentViewMode) {
          // edit -> true
          if (inEditRemainScrollPosition) {
            // 计算离得最近的 input格子
            mostTopInScreenInput();
          } else {
            setTimeout(() => {
              // 第一个可以编辑的项目
              const firstEditableInput = document
                .getElementById('dmr-content-container')
                ?.querySelector('input:not([disabled])');
              if (firstEditableInput) {
                (firstEditableInput as any)?.focus();
                (firstEditableInput as any)?.click();
              }
            }, 0);
          }
        } else {
          let result = await onDmrCardChangeInterceptor('取消编辑');
          if (result) {
            return;
          }
          // 刷新
          if (form.getFieldValue('formEdited')) {
            getDmrCardInfo(currentDmrHisId, false);
          }
        }
        setViewMode(currentViewMode);
        break;
      case 'SAVE':
        // TODO 需要删除
        // let errorKeys = formValidation(form);
        // borderErrorInputs(errorKeys);
        if (noEditSave === false) {
          if (!form.getFieldValue('formEdited')) {
            console.log('未改动');
            return;
          }
        }
        if (currentDmrHisId) {
          // 给定默认值 诸如 身份证不填给 - 这种 使其跳过 validation
          defaultValueToFormValueBeforeValidation(form);
          let errorKeys =
            extraProps?.extra?.preReview === false
              ? []
              : await formValidation(form);
          borderErrorInputs(errorKeys, true);
          if (errorKeys?.length === 0) {
            message.success('保存中，请稍后');
            // setDmrCardInfoLoading(true);
            Emitter.emit('MODAL_LOADING_STATUS', true);

            let saveResponse = await saveCardInfoV2(
              originDmrCardInfo,
              mergeFormFieldsValue(form, babyForm),
              currentDmrHisId,
              globalState?.dictData?.['Dmr'],
              dmrProcessorInstance,

              extraProps?.extra?.saveInterfaceUrl,
            );

            // setDmrCardInfoLoading(false);
            Emitter.emit('MODAL_LOADING_STATUS', false);
            form.setFieldValue('formEdited', false);
            // 更新一下 table的数据
            updateTableData();

            if (saveResponse?.code === 0 && saveResponse?.statusCode === 200) {
              // 同步EndReview
              // 当且仅当 保存成功的时候才会 同步结束Review
              preCheckboxContainerRef?.current?.endReviewWhenTaskIsReviewing();

              if (saveResponse?.data?.SucceededFlag) {
                message.success('保存成功，审核通过');
                // 保存成功的话光标跳到 搜索框上
                setRightContainerOpen(forceDmrRightReview === true);
                Emitter.emit(EventConstant.DMR_CARD_SAVE_REVIEWS, {});
                if (
                  saveAuditNoErrorAutoNext === true &&
                  forceDmrRightReview === false
                ) {
                  // 当且仅当 保存没错的时候才会自动跳转到下一个
                  message.success('质控审核通过，将自动跳转到下一份');
                  onOperationItemClick('NEXT');
                } else {
                  // 保存成功的话光标跳到 搜索框上
                  Emitter.emit(EventConstant.DMR_HEADER_FOCUS);
                }
              } else {
                buildMessageOnSubmit(saveResponse?.data?.Reviews);
                if (preCheckContainerPosition === 'bottom') {
                  document.getElementById('content').style.overflowY = 'hidden';
                }
                setRightContainerOpen(true);
                setRightCommentContainerOpen(false);
                Emitter.emit(
                  EventConstant.DMR_CARD_SAVE_REVIEWS,
                  saveResponse?.data,
                );
              }

              // 登记状态
              if (saveResponse?.data?.Extra?.['RegisterStatusName']) {
                setRegisterStatusName(
                  saveResponse?.data?.Extra?.['RegisterStatusName'],
                );
              }
              // 合并keys 到 form value里面
              Object.keys(saveResponse?.data?.Extra ?? {})?.forEach((key) => {
                form.setFieldValue(key, saveResponse?.data?.Extra?.[key]);
              });

              if (checkOnSave) {
                setTimeout(() => {
                  Emitter.emit(EventConstant.DMR_CHSGROUP_AUDIT, {
                    hisId: currentDmrHisId,
                    originDmrCardInfo: originDmrCardInfo,
                    formFieldsValue: mergeFormFieldsValue(form, babyForm),
                  });
                }, 0);
              }
              // 病案签收
              if (signinOnSave) {
                Emitter.emit(EventConstant.DMR_SIGNIN_FOR_TRACE_MODAL, true);
              }
            } else {
              if (saveResponse?.statusCode !== 400) {
                message.error('保存出现问题');
                if (location.search) {
                  let urlParam = qs.parse(location.search, {
                    ignoreQueryPrefix: true,
                  });
                  let currentHisId =
                    extraProps?.extra?.hisId ??
                    decodeURIComponent(urlParam?.hisId);
                  let instantAudit =
                    extraProps?.extra?.instantAudit ??
                    decodeURIComponent(urlParam?.instantAudit);
                  getDmrCardInfo(currentHisId, instantAudit);
                }
              }
            }
          }
        } else {
          message.error('HisId不存在，请检查');
        }
        break;
      case 'SAVE_AFTER_CHECK':
        // TODO 需要删除
        // let errorKeys = formValidation(form);
        // borderErrorInputs(errorKeys);
        if (noEditSave === false) {
          if (!form.getFieldValue('formEdited')) {
            console.log('未改动');
            return;
          }
        }
        if (currentDmrHisId) {
          // 给定默认值 诸如 身份证不填给 - 这种 使其跳过 validation
          defaultValueToFormValueBeforeValidation(form);
          let errorKeys =
            extraProps?.extra?.preReview === false
              ? []
              : await formValidation(form);
          borderErrorInputs(errorKeys, true);
          if (errorKeys?.length === 0) {
            message.success('调用质控审核中，请稍后');
            // setDmrCardInfoLoading(true);
            Emitter.emit('MODAL_LOADING_STATUS', true);

            // 先质控 再保存
            let checkResponse: RespVO<CardBundleCheck> =
              await preCheckContainerRef?.current?.bundleCheck({
                hisId: currentDmrHisId,
                originDmrCardInfo: originDmrCardInfo,
                formFieldsValue: form.getFieldsValue(true),
              });

            if (
              checkResponse?.data?.QualityCheckResult?.PassCheckFlag !== true
            ) {
              Emitter.emit('MODAL_LOADING_STATUS', false);
              message.error('审核不通过，存在错误');
              setRightContainerOpen(true);
              setRightCommentContainerOpen(false);
              return;
            }

            onOperationItemClick('SAVE', true);
          }
        } else {
          message.error('HisId不存在，请检查');
        }
        break;
      case 'SIGNIN_FOR_TRACER':
        // 病案签收
        Emitter.emit(EventConstant.DMR_SIGNIN_FOR_TRACE_MODAL, true);
        break;
      case 'CHSGROUP_AUDIT':
        // TODO 预留validation方法
        let errorKeys = auditValidation(mergeFormFieldsValue(form, babyForm));
        if (errorKeys?.length > 0) {
          borderErrorInputs(errorKeys);
          return;
        }
        Emitter.emit(EventConstant.DMR_CHSGROUP_AUDIT, {
          hisId: currentDmrHisId,
          originDmrCardInfo: originDmrCardInfo,
          formFieldsValue: mergeFormFieldsValue(form, babyForm),
        });
        if (preCheckContainerPosition === 'bottom') {
          document.getElementById('content').style.overflowY = 'hidden';
        }
        setRightContainerOpen(true);
        setRightCommentContainerOpen(false);
        break;
      case 'PREVIOUS':
        if (searchedHisIds?.length > 0 && currentDmrHisId) {
          if (extraProps?.extra?.operateNextPreviousExtraData === true) {
            let currentHisIdIndex = searchedHisIds?.findIndex(
              (item) => item === currentDmrHisId,
            );
            if (currentHisIdIndex !== 0) {
              let previousHisId = searchedHisIds?.[currentHisIdIndex - 1];
              if (!isEmptyValues(extraProps?.extra)) {
                extraProps['extra']['hisId'] = previousHisId;
              }
              setOperateNextPreviousExtraRefreshCount(
                operateNextPreviousExtraRefreshCount + 1,
              );
            } else {
              if (currentHisIdIndex === 0) {
                message.success('这是第一份病案');
              }
            }
          } else {
            // 实时去拿 当前 查询页面 这个HisId 前面的所有HisId要判定当前HisId 是不是 存在列表里面
            let previousHisId =
              await dmrLeftSearchContainerRef?.current?.getCurrentHisIdPreviousHisId(
                currentDmrHisId,
              );
            if (!isEmptyValues(previousHisId)) {
              let result = await onDmrCardChangeInterceptor();
              if (result) {
                return;
              }
              let dmrParam = {
                hisId: previousHisId,
              };
              //172.16.4.217:8088/UniDmrCore/unidmrweb/-/tags
              http: history.replace(
                `${location?.pathname}?${qs.stringify(dmrParam)}`,
              );
            } else {
              message.success('这是第一份病案');
            }
            // if (currentHisIdIndex !== 0) {
            //   let result = await onDmrCardChangeInterceptor();
            //   if (result) {
            //     return;
            //   }
            //   let dmrParam = {
            //     hisId: searchedHisIds?.[currentHisIdIndex - 1],
            //   };
            //   Emitter.emit(
            //     EventConstant.DMR_NEXT,
            //     searchedHisIds?.[currentHisIdIndex - 1],
            //   );
            //   history.replace(
            //     `${location?.pathname}?${qs.stringify(dmrParam)}`,
            //   );
            // } else {
            //   if (currentHisIdIndex === 0) {
            //     message.success('这是第一份病案');
            //   }
            // }
          }
        }
        break;
      case 'NEXT':
        if (searchedHisIds?.length > 0 && currentDmrHisId) {
          let currentHisIdIndex = searchedHisIds?.findIndex(
            (item) => item === currentDmrHisId,
          );

          if (extraProps?.extra?.operateNextPreviousExtraData === true) {
            if (currentHisIdIndex !== searchedHisIds?.length - 1) {
              let nextHisId = searchedHisIds?.[currentHisIdIndex + 1];
              if (!isEmptyValues(extraProps?.extra)) {
                extraProps['extra']['hisId'] = nextHisId;
              }
              setOperateNextPreviousExtraRefreshCount(
                operateNextPreviousExtraRefreshCount + 1,
              );
            } else {
              let nextPageHisIds =
                await extraProps?.extra?.onExtraSearchedHisIdLastOne();
              let latestSearchedHisIds = uniq(
                (extraProps?.extra?.nextPageHisIdsReplace === true
                  ? []
                  : searchedHisIds
                )?.concat(nextPageHisIds),
              );
              if (
                isEmptyValues(latestSearchedHisIds) ||
                latestSearchedHisIds[latestSearchedHisIds?.length - 1] ===
                  currentHisIdIndex
              ) {
                // 因为最后一个 index 还是原来的 或者 就是为空数组了
                message.success('这是最后一份病案');
                return;
              } else {
                let nextHisId =
                  latestSearchedHisIds?.[
                    extraProps?.extra?.nextPageHisIdsReplace === true
                      ? 0
                      : currentHisIdIndex + 1
                  ];
                if (!isEmptyValues(extraProps?.extra)) {
                  extraProps['extra']['hisId'] = nextHisId;
                }
                setOperateNextPreviousExtraRefreshCount(
                  operateNextPreviousExtraRefreshCount + 1,
                );
              }
              setSearchedHisIds(latestSearchedHisIds);
            }
          } else {
            if (currentHisIdIndex !== searchedHisIds?.length - 1) {
              let result = await onDmrCardChangeInterceptor();
              if (result) {
                return;
              }
              let dmrParam = {
                hisId: searchedHisIds?.[currentHisIdIndex + 1],
              };
              Emitter.emit(
                EventConstant.DMR_NEXT,
                searchedHisIds?.[currentHisIdIndex + 1],
              );
              history.replace(
                `${location?.pathname}?${qs.stringify(dmrParam)}`,
              );
            } else {
              if (lastDmrRecordPageReached) {
                message.success('这是最后一份病案');
              } else {
                Emitter.emit(EventConstant.DMR_NEXT_PAGE);
              }
            }
          }
        }
        break;
      case 'MEDICAL_RECORDS':
        // api/Dmr/DmrCard/GetCardEmrUrl
        let patNo = form.getFieldValue('PatNo');
        let outDate = form.getFieldValue('OutDate');
        let patAdmNo = form.getFieldValue('PatAdmNo');
        let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
        let employeeCode = userInfo?.EmployeeCode;
        message.loading('调用电子病例中');
        if (patNo && outDate) {
          if (fakeMedicalRecord) {
            window.open(
              'http://116.228.195.252:6200/Dmr/Browser/Website?patno=664717&outdate=2007-04-29',
            );
          } else {
            let medicalRecordResponse: RespVO<MedicalRecord> =
              await getDmrMedicalRecordUrl({
                HisId: currentDmrHisId,
                PatNo: patNo,
                OutDate: dayjs(outDate).format('YYYY-MM-DD'),
                PatAdmNo: patAdmNo,
                EmployeeCode: employeeCode,
                BarCode: originDmrCardInfo?.CardFlat?.['BarCode'],
                TrackNo: originDmrCardInfo?.CardFlat?.['TrackNo'],
              });
            if (
              medicalRecordResponse?.code === 0 &&
              medicalRecordResponse?.statusCode === 200
            ) {
              if (
                medicalRecordResponse?.data?.state === 200 &&
                medicalRecordResponse?.data?.payload
              ) {
                window.open(medicalRecordResponse?.data?.payload);
              } else {
                message.error('电子病例返回出现错误');
              }
            } else {
              message.error('调用电子病例出现错误，请联系管理员');
            }
          }
        } else {
          message.error('病案号或出院时间不存在');
        }
        break;
      case 'PRINT':
        message.loading('生成病案首页中');
        let exportResponse = await dmrExportBaseInfoReqV2(
          originDmrCardInfo,
          mergeFormFieldsValue(form, babyForm),
          currentDmrHisId,
          globalState?.dictData?.['Dmr'],
          dmrProcessorInstance,
        );

        if (exportResponse?.response) {
          downloadFile(
            `住院病案首页-${originDmrCardInfo?.CardFlat?.PatName}`,
            exportResponse?.response,
          );
        }
        break;
      case 'FULLSCREEN':
        // let baseUrl = '/dmr/index/fullscreen';
        // window.open(`${baseUrl}${location.search}`);
        setIsFullScreen(true);
        setTimeout(() => {
          dmrPreCardCommentRef?.current?.updatePolyLine();
        }, 0);
        break;
      case 'NORMAL_SCREEN':
        setIsFullScreen(false);
        setDmrReadonlyVisible(false);
        removeDiffItemOnChangeHisId();
        setTimeout(() => {
          dmrPreCardCommentRef?.current?.updatePolyLine();
        }, 0);
        break;
      case 'CENTER':
        let urlParam = {
          center: !centerInputActivate,
        };
        if (location.search) {
          urlParam = {
            ...qs.parse(location.search, {
              ignoreQueryPrefix: true,
            }),
            ...urlParam,
          };
        }
        history.replace(`${location?.pathname}?${qs.stringify(urlParam)}`);
        break;
      case 'CHS':
        if (!isEmptyValues(currentDmrHisId)) {
          window.open(
            `/chs/main/index?hisId=${encodeURIComponent(currentDmrHisId)}`,
          );
        }
        break;
      case 'CLEAR':
        let result = await onDmrCardChangeInterceptor('清空数据');
        if (result) {
          return;
        }
        onDmrClear();
        break;
      case 'RESET':
        form.resetFields();
        form.setFieldValue('formEdited', false);
        setViewMode(false);
        let instantAudit = extraProps?.extra?.instantAudit;
        getDmrCardInfo(currentDmrHisId, instantAudit);
        break;
      case 'EMR_ICDE_OPER':
        icdePortalContainerRef?.current?.showStatus({
          status: true,
          dmrDataShow: false,
        });
        operPortalContainerRef?.current?.showStatus({
          status: true,
          dmrDataShow: false,
        });
        break;
      case 'DMR_ICDE_DATA':
        icdePortalContainerRef?.current?.showStatus({
          status: true,
          dmrDataShow: true,
        });
        break;
      case 'DMR_OPER_DATA':
        operPortalContainerRef?.current?.showStatus({
          status: true,
          dmrDataShow: true,
        });
        break;
      case 'CHARGE_FEE':
        chargePortalContainerRef?.current?.showStatus({
          status: true,
        });
        break;
      // 独立第三方质控
      case 'THIRD_PARTY_CHECK':
        dmrBundleThirdPartyCheckReq(
          currentDmrHisId,
          originDmrCardInfo,
          form?.getFieldsValue(),
          dmrProcessorInstance,
          globalState,
        );
        break;
      case 'EXTRA_BABY':
        babyExtraContainerRef?.current?.showStatus({
          status: true,
        });
        break;
      case 'CARD_DIFF':
        // 查看 DIFF 并高亮
        setDmrReadonlyVisible(!dmrReadonlyVisible);
        setIsFullScreen(!dmrReadonlyVisible);
        if (dmrReadonlyVisible === false) {
          removeDiffItemOnChangeHisId();
        }
        break;
      case 'DMR_CHANGE_HISTORY':
        dmrChangeHistoryContainerRef?.current?.showStatus({
          status: true,
        });
        break;
      case 'DMR_PRE_CARD_COMMENT':
        setRightCommentContainerOpen(!rightCommentContainerOpen);
        setRightContainerOpen(false);
        break;
      default:
        break;
    }
  };

  // 润了
  const onDmrClear = () => {
    form.resetFields();
    form.setFieldValue('formEdited', false);
    setViewMode(true);
    setFormCardInfo(undefined);
    setCurrentDmrHisId('');
    setOriginDmrCardInfo(null);
    // 状态
    setRegisterStatusName('');
    setDmrSignInDate(undefined);
    history.replace(`${location?.pathname}`);

    babyForm.resetFields();

    // 调用批注 清空
    if (!isEmptyValues(dmrPreCardCommentRef)) {
      dmrPreCardCommentRef?.current?.clearComment();
    }
  };

  const onDeletePress = (event) => {
    if (document?.activeElement) {
      let formKey = document?.activeElement?.id?.split('#')?.at(1);

      // 当没有formKey的时候 return 比如 顶上的输入病案号这个框
      if (isEmptyValues(formKey)) {
        return;
      }

      // 单独适配
      if (
        deleteSpecialKeysProcessorByPrefix(
          formKey,
          document?.activeElement?.id,
        ) ||
        deleteSpecialKeysProcessor[formKey]
      ) {
        formKey =
          deleteSpecialKeysProcessorByPrefix(
            formKey,
            document?.activeElement?.id,
          ) ?? deleteSpecialKeysProcessor[formKey](document?.activeElement?.id);
      }

      console.log('onDeletePress', formKey, document?.activeElement?.id);

      Emitter.emit(
        getDeletePressEventKey(formKey),
        document?.activeElement?.id,
      );

      if (!formKey?.toLowerCase()?.includes('table')) {
        form.setFieldValue(formKey, '');
      }
    }
  };

  const onPageUpDownPress = async (event, up: boolean) => {
    // 当诊断手术 弹出的时候 停了pageup pagedown
    let keyboardVisible = await isKeyboardFriendlyDropdownVisible(
      'icde-oper-dropdown-container',
    );
    if (keyboardVisible === true) {
      return;
    }

    if (dmrPageUpDownSwitchModule === true) {
      rightMenuContainerRef?.current?.onLeftMenuClickOffset(
        up === true ? -1 : 1,
      );
    } else {
      defaultPageUpDownHandler(up);
    }
  };

  const onIcdeOperTransferTableAddClick = (eventName) => {
    if (eventName) {
      Emitter.emit(eventName);
    }
  };
  // 更新隐藏表单字段 与实际表格数据的同步
  const updateTableData = () => {
    // 'diagnosis-table',
    //   'diagnosisTable',
    //   'operation-table',
    //   'operationTable',
    //   'formEdited',
    //   'pathological-diagnosis-table',
    //   'pathologicalDiagnosisTable',
    //   'icu-table',
    //   'icuTable',

    console.log('form value', form.getFieldsValue(true));

    form.setFieldValue(
      'diagnosisTable',
      form.getFieldValue('diagnosis-table') ?? [],
    );
    form.setFieldValue(
      'operationTable',
      form.getFieldValue('operation-table') ?? [],
    );
    form.setFieldValue(
      'pathologicalDiagnosisTable',
      form.getFieldValue('pathological-diagnosis-table') ?? [],
    );
    form.setFieldValue('icuTable', form.getFieldValue('icu-table') ?? []);

    console.log('formValues updateTableData', form.getFieldsValue());
  };

  const resetTableData = () => {
    hiddenFormItemTableKeys?.forEach((key) => {
      if (!key?.includes('-')) {
        form.setFieldValue(key, []);
      }
    });
  };

  const resetFormItemData = () => {
    hiddenFormItemKeys?.forEach((key) => {
      if (!key?.includes('-')) {
        form.setFieldValue(key, '');
      }
    });
  };
  // header那一排按钮的可见
  const canOperatorEnabled = (item: any) => {
    if (extraProps?.extra?.operateNextPreviousExtraData === true) {
      if (item?.key === 'PREVIOUS' || item?.key === 'NEXT') {
        return true;
      }
    }

    // 只读下 不可能存在保存 / 编辑 /清空
    if (dmrEditOperationKeys?.includes(item.key)) {
      if (dmrRegistrationReadOnly === true) {
        return false;
      }
    }

    if (isFullScreen === true) {
      if (item?.key === 'FULLSCREEN') {
        return false;
      }
    }

    if (isFullScreen === false) {
      if (item?.key === 'NORMAL_SCREEN') {
        return false;
      }
    }

    if (item?.key === 'DMR_PRE_CARD_COMMENT') {
      return preCardExamineType === 'comment' && preCardExamineEnable === true;
    }

    let mergedFlag = undefined;
    if (extraProps?.extra?.onOperationExtraProcess) {
      let result = extraProps?.extra?.onOperationExtraProcess(item?.key);
      if (result !== undefined) {
        mergedFlag = result;
      }
    }

    if (item?.key === 'DOCTOR_EMR') {
      return enableDoubleDeckDoctorEmr && isFullScreen;
    }

    if (!isEmptyValues(dmrOperatorConfig ?? {})) {
      if (mergedFlag === undefined) {
        mergedFlag = (dmrOperatorConfig ?? {})?.[item.key]?.enable ?? true;
      } else {
        mergedFlag =
          mergedFlag && ((dmrOperatorConfig ?? {})?.[item.key]?.enable ?? true);
      }
    }

    if (mergedFlag !== undefined) {
      return mergedFlag;
    }

    return item?.enable ?? true;
  };

  // 头部操作按钮Render
  const OperationHeader = ({ form, operatorConfig }) => {
    const [saveEnable, setSaveEnable] = useState(
      form.getFieldValue('formEdited') ?? false,
    );

    useEffect(() => {
      Emitter.on(EventConstant.DMR_FORM_VALUE_CHANGE, (containerId: string) => {
        form.setFieldValue('formEdited', true);
        setSaveEnable(true);

        let formValues = form?.getFieldsValue();
        // 通知 icde oper
        icdePortalContainerRef?.current?.updateDmrDataValue(formValues);
        operPortalContainerRef?.current?.updateDmrDataValue(formValues);

        // diff
        let readonlyCardInfo =
          dmrReadonlyContainerRef?.current?.getReadonlyCardFormInfo();
        if (isEmptyValues(readonlyCardInfo)) {
          return;
        }

        if (!isEmptyValues(containerId)) {
          let changedValues = form?.getFieldValue(containerId);
          let changedValueItem = {};
          changedValueItem[containerId] = changedValues;

          setTimeout(() => {
            diffCardInEditing(changedValueItem, readonlyCardInfo);
          }, 100);
        }
      });

      Emitter.on(EventConstant.DMR_FORM_VALUE_RESET_WITH_DEFAULT_VALUE, () => {
        form.setFieldValue('formEdited', false);
        setSaveEnable(false);
      });

      return () => {
        Emitter.off(EventConstant.DMR_FORM_VALUE_CHANGE);
        Emitter.off(EventConstant.DMR_FORM_VALUE_RESET_WITH_DEFAULT_VALUE);
      };
    }, []);

    return (
      <div
        id={'dmr-root-operation-header'}
        className={'dmr-root-operation-header'}
      >
        {extraProps?.extra?.queryHeaderShow !== false && (
          <DmrQueryHeader
            onDmrCardChangeInterceptor={onDmrCardChangeInterceptor}
          />
        )}

        <div
          style={{ flexFlow: 'row wrap', rowGap: 4, marginLeft: 'auto' }}
          className={'flex-row-center'}
        >
          {operations(viewMode, currentDmrHisId, searchedHisIds, {
            babyTitleContainerRef: babyTitleContainerRef,
            babyTitleDefaultCount: babyForm?.getFieldValue('babies')?.length,
            dmrSignInDate: dmrSignInDate,
            registerStatusName: registerStatusName,
            dmrReadonlyVisible: dmrReadonlyVisible,
          })
            ?.filter((item) => {
              return canOperatorEnabled(item);
            })
            ?.map((item) => {
              if (!isEmptyValues(operatorConfig)) {
                item['order'] = operatorConfig?.[item.key]?.order ?? 0;
              }

              return item;
            })
            ?.sort((a, b) => a?.order - b?.order)
            .map((item) => {
              return (
                <Button
                  className={'operation-item'}
                  // type="primary"
                  // ghost
                  size={'small'}
                  disabled={
                    item?.key !== 'SAVE' && item?.key !== 'SAVE_AFTER_CHECK'
                      ? item?.disabled
                      : noEditSave
                      ? item?.disabled
                      : !saveEnable || item?.disabled
                  }
                  onClick={operationClickDebounce(() =>
                    onOperationItemClick(item.key),
                  )}
                >
                  {item?.icon
                    ? React.cloneElement(item?.icon, {
                        style: {
                          color: item?.color ?? 'unset',
                          '--customStrokeColor': item?.color,
                        },
                        className: `${
                          item?.color ? 'operation-icon-custom-stroke' : ''
                        }`,
                      })
                    : null}
                  {typeof item?.title === 'string' ? (
                    <span style={{ marginLeft: 8 }}>{item.title}</span>
                  ) : (
                    item?.title
                  )}
                </Button>
              );
            })}
        </div>
      </div>
    );
  };

  // GridItemContainer
  const GridItemContainer = (props) => {
    const sectionStyle = props?.item?.data?.component?.includes('Section')
      ? { background: '#ffffff !important' }
      : {};

    // 表示是 SectionHeader / SectionBottom
    if (props?.item?.data?.component?.includes('Section')) {
      // 隐式 header  / bottom
      if (props?.item?.data?.props?.hideInDmr === true) {
        return (
          <div
            id={props?.id}
            key={props?.key}
            style={{ ...props?.style, ...sectionStyle }}
          />
        );
      }
    }

    return (
      <div
        id={props?.id}
        key={props?.key}
        style={{ ...props?.style, ...sectionStyle }}
        className={`grid-stack-item ${props?.className ?? ''} `}
        gs-id={props?.id}
        gs-x={props?.item?.x}
        gs-y={props?.item?.y}
        gs-w={props?.item?.w}
      >
        <GridItem
          containerStyle={props?.style}
          containerClassName={props?.className}
          form={form}
          index={props?.index}
          componentId={props?.item.data.key}
          key={uuidv4()}
          data={props?.item.data}
          enableGridItemComment={extraProps?.extra?.enableGridItemComment}
        />
      </div>
    );
  };

  // 所有Layout渲染的地方（headerLayout + contentLayout）
  const dmrChildren = React.useMemo(() => {
    return layouts[breakpoint]?.map((item, index) => {
      return (
        <GridItemContainer
          id={item.data.key}
          key={item.data.key}
          item={item}
          index={index}
        />
        // </GridItemContext.Provider>
      );
    });
  }, [layouts]);

  // 同步滚动
  const onDmrContentContainerScroll = React.useCallback(
    debounce((event) => {
      console.log('Content Scroll', event);

      let readonlyContainer = document.getElementById('dmr-readonly-container');

      // 没容器 省的计算
      if (
        (readonlyContainer === null || readonlyContainer === undefined) &&
        extraProps?.extra?.detailCommentRef === null
      ) {
        return;
      }

      const childElements = Array.from(
        document?.getElementById('dmr-content-grid-layout')?.children ?? [],
      )?.filter((item) => {
        return !item?.id?.toLowerCase()?.includes('bottom');
      });
      let closestElement = null;
      let closestDistance = Infinity;

      const container = document.getElementById('dmr-content-container');

      const containerRect = container.getBoundingClientRect();

      const distanceMargin = 30;

      for (let child of childElements) {
        const rect = child.getBoundingClientRect();
        const distance = rect.top + rect.height - containerRect.top;

        if (distance >= distanceMargin && distance < closestDistance) {
          closestDistance = distance;
          closestElement = child;
        }
      }

      console.log('closestElement', closestElement);

      if (closestElement) {
        if (readonlyContainer) {
          setTimeout(() => {
            // const containerTop = document?.getElementById("readonly-content-container")?.parentElement?.getBoundingClientRect().top
            // const elementY = document?.getElementById(`Readonly-formItem#${closestElement?.id}`)?.getBoundingClientRect()?.y;
            // document?.getElementById("readonly-content-container")?.parentElement?.scrollTo({
            //   top: elementY - containerTop,
            //   behavior: "smooth",
            // })
            document
              ?.getElementById(`Readonly-formItem#${closestElement?.id}`)
              ?.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'nearest',
              });
          }, 300);
        }

        if (extraProps?.extra?.detailCommentRef) {
          extraProps?.extra?.detailCommentRef?.current?.scrollAlongWithDmr(
            closestElement,
          );
        }

        if (dmrPreCardCommentRef) {
          dmrPreCardCommentRef?.current?.onContentScrollForContent(
            closestElement,
          );
        }
      }
    }, 100),
    [],
  );

  console.log('isEdited', form.isFieldsTouched(true));

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    }),
  );

  let defaultWidth =
    document.getElementById('dmr-form-container')?.getBoundingClientRect()
      ?.width - 19;
  console.log('defaultWidth', defaultWidth);

  // @ts-ignore
  return (
    <>
      <div
        tabIndex={0}
        ref={mergeRefs(containerShortcutsRefs)}
        id={'dmr-container-common'}
        style={{
          ...(extraProps?.extra?.commonContainerStyle ?? {}),
          zoom: zoomLevel || 1,
          outline: 'none',
        }}
        className={`dmr-container-common ${
          isFullScreen ? 'dmr-fullscreen-container' : 'dmr-normal-container'
        }`}
      >
        <div
          id={'dmr-base-container'}
          className="ant-card"
          style={{ height: '100%' }}
        >
          <DndContext
            sensors={sensors}
            onDragEnd={(event) => {
              if (event?.active?.id === 'emr-icde-container') {
                icdePortalContainerRef?.current?.updateCoordinates(
                  event?.delta,
                );
              }

              if (event?.active?.id === 'emr-oper-container') {
                operPortalContainerRef?.current?.updateCoordinates(
                  event?.delta,
                );
              }

              if (event?.active?.id === 'charge-data-container') {
                chargePortalContainerRef?.current?.updateCoordinates(
                  event?.delta,
                );
              }

              if (event?.active?.id === 'change-history-container') {
                dmrChangeHistoryContainerRef?.current?.updateCoordinates({
                  delta: event?.delta,
                  id: 'change-history-container',
                });
              }

              if (event?.active?.id === 'change-history-detail-container') {
                dmrChangeHistoryContainerRef?.current?.updateCoordinates({
                  delta: event?.delta,
                  id: 'change-history-detail-container',
                });
              }
            }}
            modifiers={[restrictToParentElement]}
          >
            {ReactDOM.createPortal(
              <IcdeDataPortal
                form={form}
                containerRef={icdePortalContainerRef}
                hisId={currentDmrHisId}
                dictData={globalState?.dictData}
              />,
              document.getElementById('root-container'),
            )}

            {ReactDOM.createPortal(
              <OperDataPortal
                form={form}
                containerRef={operPortalContainerRef}
                hisId={currentDmrHisId}
                dictData={globalState?.dictData}
              />,
              document.getElementById('root-container'),
            )}

            {ReactDOM.createPortal(
              <ChargePortal
                containerRef={chargePortalContainerRef}
                hisId={currentDmrHisId}
                dictData={globalState?.dictData}
              />,
              document.getElementById('root-container'),
            )}

            {ReactDOM.createPortal(
              <DmrChangeHistoryPortal
                containerRef={dmrChangeHistoryContainerRef}
                hisId={currentDmrHisId}
                dictData={globalState?.dictData}
              />,
              document.getElementById('root-container'),
            )}

            <ShortcutsHelpModal shortcuts={shortcuts} />

            {/*批注*/}
            <CommentModal hisId={currentDmrHisId} />

            {/*示踪*/}
            <TraceModal barCode={originDmrCardInfo?.CardFlat?.BarCode} />

            {/* 示踪病案签收 */}
            <SinginForTraceModal
              barCode={originDmrCardInfo?.CardFlat?.BarCode}
              dmrSignInDate={dmrSignInDate}
              onReqDone={(signInDate) => {
                setDmrSignInDate(signInDate);
              }}
            />

            <div
              id={'dmr-root-container'}
              className={'dmr-root-container'}
              style={{
                height:
                  extraProps?.extra?.rootContainerHeight ??
                  (isFullScreen
                    ? 'calc(100% - 20px)'
                    : zoomLevel === 1
                    ? document.getElementById('site-layout-content')
                        ?.offsetHeight - 50
                    : '100%'),
              }}
            >
              {/*left container*/}
              <div
                className={'dmr-left-container'}
                style={
                  extraProps?.extra?.leftContainerShow === false
                    ? {
                        visibility: 'hidden',
                      }
                    : {}
                }
              >
                <Drawer
                  placement="left"
                  mask={true}
                  maskClosable={true}
                  width={800}
                  open={leftContainerOpen}
                  className={'dmr-search-drawer-container'}
                  onClose={() => {
                    setLeftContainerOpen(false);
                  }}
                  title={`病案查询`}
                  getContainer={() => {
                    return document.getElementById('dmr-root-container');
                  }}
                >
                  <DmrSearch
                    containerRef={dmrLeftSearchContainerRef}
                    visible={leftContainerOpen}
                    onDmrCardChangeInterceptor={onDmrCardChangeInterceptor}
                    viewMode={viewMode}
                    onRightArrowClick={(status) => setLeftContainerOpen(status)}
                  />
                </Drawer>

                <div className={'dmr-left-trigger-container'}>
                  <div
                    className={'result-collapsed'}
                    onClick={() => {
                      setLeftContainerOpen(!leftContainerOpen);
                      if (leftContainerOpen === false) {
                        Emitter.emit(
                          EventConstant.DMR_SEARCH_REFRESH_DATA,
                          true,
                        );
                      }
                    }}
                  >
                    {leftContainerOpen ? (
                      <DoubleLeftOutlined />
                    ) : (
                      <DoubleRightOutlined />
                    )}
                    查询
                  </div>
                </div>
              </div>

              <div
                ref={mergeRefs([...topMenuKeysRefs])}
                id={'dmr-container'}
                className={'dmr-container'}
                style={{
                  width:
                    preCheckContainerPosition === 'right' &&
                    rightContainerOpen === true
                      ? `calc(100% - 300px)`
                      : 'calc(100% - 30px)',
                }}
              >
                {/*operation header*/}
                <div
                  id={'dmr-main-container'}
                  className={`dmr-main-container ${
                    isFullScreen && dmrReadonlyVisible === true
                      ? 'dmr-main-container-doctor-emr-enable'
                      : ''
                  }`}
                >
                  {extraProps?.extra?.anchorShow !== false && (
                    <RightMenu
                      containerRef={rightMenuContainerRef}
                      defaultTopMenuKeys={topMenuKeys}
                      initialLayout={layouts[breakpoint]}
                      inDmrRegister={true}
                      isFullscreen={isFullScreen && dmrReadonlyVisible}
                    />
                  )}

                  <OperationHeader
                    form={form}
                    operatorConfig={dmrOperatorConfig ?? {}}
                  />

                  <ModalLoading
                    eventName={'MODAL_LOADING_STATUS'}
                    spinWrapperClassName={'dmr-loading'}
                    backgroundColor={'rgba(255,255,255, 0.3)'}
                    parentContainerId={'dmr-root-container'}
                  />

                  <fieldset
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      height: `calc(100% - ${rootOperationHeight + 5}px)`,
                      width:
                        extraProps?.extra?.anchorShow !== false
                          ? dmrReadonlyVisible === true
                            ? 'calc(100% - 4px - 80px)'
                            : 'calc(100% - 4px - 10px)'
                          : 'calc(100% - 4px)',
                    }}
                    className={`${viewMode ? 'view-mode' : ''}`}
                    disabled={viewMode}
                    onCompositionStart={() => {
                      compositionStatusRef.current = true;
                    }}
                    onCompositionEnd={() => {
                      compositionStatusRef.current = false;
                    }}
                  >
                    {/* 医生端首页 用于病案室 对照 查看 */}
                    {isFullScreen && dmrReadonlyVisible && (
                      <DmrReadonly
                        dmrContainerRef={dmrContainerRef}
                        containerRef={dmrReadonlyContainerRef}
                        headerLayouts={headerLayouts?.['lg']}
                        contentLayouts={layouts?.['lg']}
                        hisId={currentDmrHisId}
                        dmrProcessorInstance={dmrProcessorInstance}
                      />
                    )}

                    <Form
                      id={'dmr-form-container'}
                      className={'dmr-form-container'}
                      style={{
                        height: '100%',
                        overflowX: 'auto',
                        margin:
                          extraProps?.extra?.anchorShow !== false
                            ? dmrReadonlyVisible === false
                              ? '0px 5px 0px 85px'
                              : '0px 5px 0px 5px'
                            : '0px 5px 0px 5px',
                      }}
                      form={form}
                      name="basic"
                      onFinish={onFinish}
                      onFinishFailed={onFinishFailed}
                      autoComplete="off"
                      wrapperCol={{ flex: 1 }}
                      onValuesChange={(changedValues, allValues) => {
                        console.log('onValuesChange', changedValues, allValues);

                        // diff
                        let readonlyCardInfo =
                          dmrReadonlyContainerRef?.current?.getReadonlyCardFormInfo();
                        if (isEmptyValues(readonlyCardInfo)) {
                          return;
                        }

                        setTimeout(() => {
                          diffCardInEditing(changedValues, readonlyCardInfo);
                        }, 100);
                      }}
                      onFieldsChange={(changedFields, allFields) => {
                        console.log('onFieldsChange', changedFields, allFields);
                        Emitter.emit(EventConstant.DMR_FORM_VALUE_CHANGE);
                      }}
                    >
                      {/*附页 */}
                      <GridItemContext.Provider
                        value={{
                          globalState: globalState,
                          dynamicComponentsMap: dynamicComponentsMap,
                          externalConfig: externalDmrConfig,
                          eventNames: {
                            HELP_MODAL: EventConstant.DMR_HELP_MODAL,
                            TABLE_NEXT_KEY: EventConstant.DMR_TABLE_NEXT_KEY,
                          },
                          modelGroup: 'Dmr',
                          extra: {
                            selectorWithNotValid:
                              extraProps?.extra?.selectorWithNotValid ?? false,
                            revisionHistoricalMode:
                              extraProps?.extra?.type === 'REVISION' ||
                              extraProps?.extra?.type === 'HISTORICAL',
                            enableGridItemComment:
                              extraProps?.extra?.enableGridItemComment,
                            detailCommentRef:
                              extraProps?.extra?.detailCommentRef,
                            // 首页批注 containerRef
                            dmrPreCardCommentContainerRef:
                              extraProps?.extra?.dmrPreCardCommentRef,
                            // 首页containerRef
                            gridContainerRef:
                              extraProps?.extra?.dmrGridContainerRef ??
                              gridContainerRef,
                          },
                        }}
                      >
                        {/*新生儿 附页 */}
                        <BabyModal
                          baseForm={form}
                          form={babyForm}
                          containerRef={babyExtraContainerRef}
                          viewMode={viewMode}
                          babyTitleContainerRef={babyTitleContainerRef}
                        />
                      </GridItemContext.Provider>

                      <ModalLoading
                        eventName={'DMR_LAYOUT_LOADING'}
                        spinWrapperClassName={'dmr-loading'}
                        backgroundColor={'rgba(255,255,255,1)'}
                        parentContainerId={'dmr-form-container'}
                      />

                      {/*dmrLayoutLoading*/}
                      <>
                        <div
                          id={'dmr-header-container'}
                          className={'dmr-header-container'}
                          style={{ minWidth: 1045, padding: '0px 5px 0px 5px' }}
                        >
                          {/* 这块是表单header */}
                          <DmrHeader
                            gridContainerRef={gridContainerRef}
                            form={form}
                            viewMode={viewMode}
                            registerStatusName={registerStatusName}
                            dmrSignInDate={dmrSignInDate}
                            layoutSizeKey={breakpoint}
                            headerLayouts={headerLayouts}
                            itemWrapper={GridItemContainer}
                            headerContentData={headerLayouts[breakpoint]}
                            extra={extraProps?.extra}
                          />
                        </div>

                        <div
                          id={'dmr-content-container'}
                          className={'dmr-content-container'}
                          style={{
                            flex: 1,
                            minWidth: 1045,
                            padding: '0px 5px 0px 5px',
                          }}
                          onScroll={(event) => {
                            containerScrollY.current = event.target.scrollTop;
                            extraProps?.extra?.svgLinesContainerRef?.current?.onContentScroll();
                            // 首页批注 线 滚动
                            dmrPreCardCommentRef?.current?.onContentScrollForLine();
                            (global?.window as any)?.eventEmitter?.emit(
                              'CONTENT_SCROLL_FOR_COMMENT',
                            );
                            onDmrContentContainerScroll(event);
                          }}
                        >
                          <>
                            {[
                              ...requireAndPreCheckRulesKeys,
                              ...hiddenFormItemTableKeys,
                              ...hiddenFormItemKeys,
                            ]?.map((key) => {
                              return <Form.Item name={key} hidden={true} />;
                            })}
                          </>

                          <Separators
                            width={defaultWidth}
                            initialLayout={layouts[breakpoint]}
                            extra={extraProps?.extra}
                          />

                          <SectionSeparators
                            width={defaultWidth}
                            initialLayout={layouts[breakpoint]}
                            extra={extraProps?.extra}
                            dmrReadonlyVisible={dmrReadonlyVisible}
                          />

                          <div
                            ref={mergeRefs([
                              ...shortcutsRefs,
                              extraProps?.extra?.dmrGridContainerRef,
                            ])}
                            id={'dmr-content-grid-layout'}
                            className="grid-stack"
                          >
                            <GridItemContext.Provider
                              value={{
                                globalState: globalState,
                                dynamicComponentsMap: dynamicComponentsMap,
                                externalConfig: externalDmrConfig,
                                eventNames: {
                                  HELP_MODAL: EventConstant.DMR_HELP_MODAL,
                                  TABLE_NEXT_KEY:
                                    EventConstant.DMR_TABLE_NEXT_KEY,
                                },
                                modelGroup: 'Dmr',
                                extra: {
                                  selectorWithNotValid:
                                    extraProps?.extra?.selectorWithNotValid ??
                                    false,
                                  revisionHistoricalMode:
                                    extraProps?.extra?.type === 'REVISION' ||
                                    extraProps?.extra?.type === 'HISTORICAL',
                                  enableGridItemComment:
                                    extraProps?.extra?.enableGridItemComment,
                                  detailCommentRef:
                                    extraProps?.extra?.detailCommentRef,
                                  // 首页批注 containerRef
                                  dmrPreCardCommentContainerRef:
                                    extraProps?.extra?.dmrPreCardCommentRef,
                                  // 首页containerRef
                                  gridContainerRef:
                                    extraProps?.extra?.dmrGridContainerRef ??
                                    gridContainerRef,
                                },
                                configurableDataIndex: configurableDataIndex,
                              }}
                            >
                              {dmrChildren}
                            </GridItemContext.Provider>
                          </div>

                          {/*说明 20250603 陈总要求先干掉*/}
                          {/* {!isEmptyValues(layouts) && (
                            <div className={'extra-explanation-container'}>
                              <div className={'flex-row'}>
                                <InputSuffix
                                  style={{
                                    flexFlow: 'row wrap',
                                  }}
                                  prefix={'说明：（一）医疗付费方式：'}
                                  hideInput={true}
                                  formKey={'medical-payment'}
                                  suffixModuleKey={'YLFKFS'}
                                  suffixModuleGroup={'Dmr'}
                                />
                              </div>

                              <span>
                                （二）凡可由医院信息系统提供住院费⽤清单的,住院病案⾸⻚中可不填写“住院费⽤”
                              </span>
                            </div>
                          )} */}
                        </div>
                      </>
                    </Form>
                  </fieldset>
                </div>
              </div>

              {preCardExamineEnable && (
                <>
                  {preCardExamineType === 'checkbox' && (
                    <PreCheckbox
                      containerRef={preCheckboxContainerRef}
                      hisId={currentDmrHisId}
                    />
                  )}
                </>
              )}

              {preCheckContainerPosition === 'bottom' &&
                extraProps?.extra?.qualityCheckShow !== false && (
                  <>
                    <div className={'dmr-bottom-quality-container'}>
                      <div
                        className={'trigger-container'}
                        onClick={() => {
                          // 先行改变 content 的style
                          document.getElementById('content').style.overflowY =
                            'hidden';
                          setRightContainerOpen(true);
                        }}
                      >
                        <UpOutlined style={{ color: '#ffffff' }} />
                      </div>
                    </div>

                    <PreCheckResult
                      hisId={currentDmrHisId}
                      containerRef={preCheckContainerRef}
                      borderErrorInputs={borderErrorInputs}
                      dmrProcessorInstance={dmrProcessorInstance}
                      preCheckModuleConfig={dmrPreCheckModuleConfig}
                      status={rightContainerOpen}
                      borderErrorInputsInTable={borderErrorInputsInTable}
                      setStatus={(status) => {
                        setRightContainerOpen(status);
                        setTimeout(() => {
                          document.getElementById('content').style.overflowY =
                            'auto';
                        }, 800);
                      }}
                    />
                  </>
                )}

              {!isEmptyValues(
                extraProps?.extra?.dmrExamineCheckContainerMountId,
              ) &&
                !isEmptyValues(
                  document.getElementById(
                    extraProps?.extra?.dmrExamineCheckContainerMountId,
                  ),
                ) &&
                ReactDOM.createPortal(
                  <PreCheckResult
                    containerRef={preCheckContainerRef}
                    borderErrorInputs={borderErrorInputs}
                    dmrProcessorInstance={dmrProcessorInstance}
                    preCheckModuleConfig={dmrPreCheckModuleConfig}
                    borderErrorInputsInTable={borderErrorInputsInTable}
                    isExtraExamineCheck={
                      !isEmptyValues(
                        extraProps?.extra?.dmrExamineCheckContainerMountId,
                      )
                    }
                  />,
                  document.getElementById(
                    extraProps?.extra?.dmrExamineCheckContainerMountId,
                  ),
                )}

              {preCheckContainerPosition === 'right' &&
                extraProps?.extra?.qualityCheckShow !== false && (
                  <div
                    className={'dmr-right-container'}
                    style={{
                      width: `${
                        rightContainerOpen
                          ? extraProps?.extra?.enableStandaloneCheck === true
                            ? 430
                            : 300
                          : 30
                      }px`,
                      minWidth: `${
                        rightContainerOpen
                          ? extraProps?.extra?.enableStandaloneCheck === true
                            ? 430
                            : 300
                          : 30
                      }px`,
                      height: 80,
                    }}
                  >
                    <div className={'dmr-right-trigger-container'}>
                      <div
                        className={'result-collapsed'}
                        onClick={() => {
                          setRightContainerOpen(!rightContainerOpen);
                        }}
                      >
                        <PreCheckIcon />
                      </div>
                    </div>
                    <div
                      className={`right-content ${
                        rightContainerOpen ? 'content-open' : 'content-close'
                      }`}
                    >
                      <PreCheckResult
                        hisId={currentDmrHisId}
                        containerRef={preCheckContainerRef}
                        borderErrorInputs={borderErrorInputs}
                        dmrProcessorInstance={dmrProcessorInstance}
                        preCheckModuleConfig={dmrPreCheckModuleConfig}
                        borderErrorInputsInTable={borderErrorInputsInTable}
                        enableStandaloneCheck={
                          extraProps?.extra?.enableStandaloneCheck
                        }
                        standaloneCheckRef={
                          extraProps?.extra?.standaloneCheckContainerRef
                        }
                      />
                    </div>
                  </div>
                )}

              {preCardExamineEnable === true &&
                preCardExamineType === 'comment' && (
                  <div
                    className={'dmr-right-comment-container'}
                    style={{
                      width: `${rightCommentContainerOpen ? 300 : 0}px`,
                      minWidth: `${rightCommentContainerOpen ? 300 : 0}px`,
                      height: '100%',
                    }}
                  >
                    <DmrRightCommentContainer
                      hisId={currentDmrHisId}
                      open={rightCommentContainerOpen}
                      containerRef={dmrPreCardCommentRef}
                      dmrGridContainerRef={gridContainerRef}
                    />
                  </div>
                )}
            </div>
          </DndContext>
        </div>
      </div>
    </>
  );
};

export const Separators = ({ width, initialLayout, extra }) => {
  const [tableWidth, setTableWidth] = useState(0);

  const [layout, setLayout] = useState(initialLayout);

  const [resizeCount, setResizeCount] = useState(0);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_TABLE_LAYOUT_CHANGE_SEPARATOR, (layout) => {
      setLayout(layout);
    });

    Emitter.on(EventConstant.TABLE_LAYOUT_CHANGE_RESET_SEPARATOR, () => {
      setLayout(undefined);
    });

    return () => {
      Emitter.off(EventConstant.DMR_TABLE_LAYOUT_CHANGE_SEPARATOR);
      Emitter.off(EventConstant.TABLE_LAYOUT_CHANGE_RESET_SEPARATOR);
    };
  }, []);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_TABLE_LAYOUT_RESIZE_SEPARATOR, (layout) => {
      let newResizeCount = resizeCount + 1;
      setResizeCount(newResizeCount);
    });

    return () => {
      Emitter.off(EventConstant.DMR_TABLE_LAYOUT_RESIZE_SEPARATOR);
    };
  }, [resizeCount]);

  const yListWithKey = {};
  (!isEmptyValues(layout) ? layout : initialLayout)
    ?.filter((item) => {
      if (item?.data?.component?.includes('Section')) {
        // 隐式 header  / bottom
        if (item?.data?.props?.hideInDmr === true) {
          return false;
        }
      }
      return true;
    })
    ?.forEach((item) => {
      if (yListWithKey[item?.y]) {
        yListWithKey[item?.y] = [
          ...yListWithKey[item?.y],
          {
            i: item?.i,
            component: item?.data?.component,
          },
        ];
      } else {
        yListWithKey[item?.y] = [
          {
            i: item?.i,
            component: item?.data?.component,
          },
        ];
      }
    });

  const yListKeys = Object.keys(yListWithKey);

  return (
    <>
      {yListKeys.map((key, index) => {
        let rowKeysItem = yListWithKey[key]?.at(0);

        let previousRowKeysItem =
          index > 0 ? yListWithKey[yListKeys[index - 1]]?.at(0) : undefined;

        let separatorItemKey = getSeparatorKey(rowKeysItem?.i);
        let currentRowItemElement = document.getElementById(rowKeysItem?.i);

        let transformHeight = currentRowItemElement?.offsetTop;

        // 第一行也不用
        if (index === 0) {
          return null;
        }

        // 当前行 是section 就返回空
        if (rowKeysItem?.component?.includes('Section')) {
          return null;
        }

        // 上一行是 section 也返回空
        if (previousRowKeysItem?.component?.includes('Section')) {
          return null;
        }

        return (
          <div
            id={separatorItemKey}
            key={separatorItemKey}
            className={
              extra?.anchorShow !== false
                ? 'separator-container'
                : 'separator-container-full-width'
            }
            style={{
              top: transformHeight - 2,
            }}
          >
            <div className={'separator'} />
          </div>
        );
      })}
    </>
  );
};

export const SectionSeparators = ({
  width,
  initialLayout,
  extra,
  dmrReadonlyVisible,
}) => {
  const [layout, setLayout] = useState(initialLayout);

  const [resizeCount, setResizeCount] = useState(0);

  useEffect(() => {
    Emitter.on(
      EventConstant.DMR_TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR,
      (layout) => {
        setLayout(layout);
      },
    );

    Emitter.on(
      EventConstant.DMR_TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR,
      () => {
        setLayout(undefined);
      },
    );

    return () => {
      Emitter.off(EventConstant.DMR_TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR);
      Emitter.off(
        EventConstant.DMR_TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR,
      );
    };
  }, []);

  useEffect(() => {
    Emitter.on(
      EventConstant.DMR_TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR,
      (layout) => {
        let newResizeCount = resizeCount + 1;
        setResizeCount(newResizeCount);
      },
    );

    return () => {
      Emitter.off(EventConstant.DMR_TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR);
    };
  }, [resizeCount]);

  let sectionSeparators = [];

  let sectionSeparatorItem: any = {};
  (!isEmptyValues(layout) ? layout : initialLayout)
    ?.filter((item) => {
      return (
        item?.data?.component === 'SectionHeader' ||
        item?.data?.component === 'SectionBottom'
      );
    })
    ?.forEach((item) => {
      if (item?.data?.component === 'SectionHeader') {
        let headerItem = document.getElementById(item?.i);
        // 不存在 item
        if (isEmptyValues(sectionSeparatorItem)) {
          sectionSeparatorItem = {
            top: headerItem?.offsetTop + headerItem?.offsetHeight,
            backgroundColor: item?.data?.props?.sectionHeaderBorderColor,
            id: item?.i,
          };
        } else {
          // 存在item那就直接返回上一行
          sectionSeparatorItem['height'] =
            headerItem?.offsetTop - sectionSeparatorItem?.['top'];
          sectionSeparators.push(sectionSeparatorItem);
          sectionSeparatorItem = {};
          // 因为现在是header bottom一体
          // 从第二个开始 也要这样 先重置 然后再追加一个 以表示下一个开始
          sectionSeparatorItem = {
            top: headerItem?.offsetTop + headerItem?.offsetHeight,
            backgroundColor: item?.data?.props?.sectionHeaderBorderColor,
            id: item?.i,
          };
        }
      }

      if (item?.data?.component === 'SectionBottom') {
        let bottomItem = document.getElementById(item?.i);
        if (!isEmptyValues(sectionSeparatorItem)) {
          sectionSeparatorItem['height'] =
            bottomItem?.offsetTop - sectionSeparatorItem?.['top'];
          sectionSeparators.push(sectionSeparatorItem);
          sectionSeparatorItem = {};
        }
      }
    });

  // 可能最下面没有 但是建议 一对 TODO
  if (!isEmptyValues(sectionSeparatorItem)) {
  }

  console.log('sectionSeparators', sectionSeparators);

  return (
    <>
      {sectionSeparators
        ?.filter((item) => {
          return item?.height > 0 && item?.top >= 0;
        })
        ?.map((item) => {
          return (
            <>
              <div
                id={`${item?.id}-LEFT`}
                className={'section-separator'}
                style={{
                  left: 5,
                  height: item?.height,
                  top: item?.top,
                  backgroundColor: item?.backgroundColor,
                }}
              />
              <div
                id={`${item?.id}-RIGHT`}
                className={'section-separator'}
                style={{
                  right: 5,
                  height: item?.height,
                  top: item?.top,
                  backgroundColor: item?.backgroundColor,
                }}
              />
            </>
          );
        })}
    </>
  );
};

interface DmrHeaderProps {
  form?: FormInstance;
  viewMode: boolean;
  registerStatusName: string;
  dmrSignInDate?: string;
  layoutSizeKey?: string;
  headerLayouts: any;
  headerContentData: any;

  // configuration
  isDraggable?: boolean;
  isResizable?: boolean;
  onLayoutChange?: (layout, layouts) => void;
  onDragStop?: (
    newLayout,
    oldDragItem,
    newDragItem,
    placeHlder,
    e,
    node,
  ) => void;
  itemWrapper: React.FC;

  layoutForm?: any;

  extra?: DmrExtraProps;

  gridContainerRef: any;
}

export const DmrHeader = (props: DmrHeaderProps) => {
  const dmrHeaderChildren = React.useMemo(() => {
    return props?.headerContentData?.map((item, index) => {
      let ItemWrapper = props?.itemWrapper;
      let itemWrapperProps = {
        id: item.data.key,
        key: item.data.key,
        item: item,
        index: index,
        type: 'HEADER',
        layoutForm: props?.layoutForm,
      };
      return (
        <GridItemContext.Provider
          value={{
            globalState: globalState,
            dynamicComponentsMap: dynamicComponentsMap,
            externalConfig: externalDmrConfig,
            eventNames: {
              HELP_MODAL: EventConstant.DMR_HELP_MODAL,
              TABLE_NEXT_KEY: EventConstant.DMR_TABLE_NEXT_KEY,
            },
            modelGroup: 'Dmr',
            extra: {
              selectorWithNotValid: props?.extra?.selectorWithNotValid ?? false,
              enableGridItemComment: props?.extra?.enableGridItemComment,
              detailCommentRef: props?.extra?.detailCommentRef,
              // 首页批注 containerRef
              dmrPreCardCommentContainerRef: props?.extra?.dmrPreCardCommentRef,
              // 首页containerRef
              gridContainerRef:
                props?.extra?.dmrGridContainerRef ?? props?.gridContainerRef,
            },
          }}
        >
          <ItemWrapper {...itemWrapperProps} />
        </GridItemContext.Provider>
      );
    });
  }, [props?.headerContentData]);

  let defaultWidth =
    document.getElementById('dmr-form-container')?.getBoundingClientRect()
      ?.width - 19;
  console.log('defaultWidth', defaultWidth);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const hospitalName = globalState?.dictData?.['Hospital']?.find((item) => {
    return item?.Code === props?.form.getFieldValue('HospCode');
  })?.Name;

  return (
    <div className={'dmr-header'}>
      <div className={'header-title'}>
        <span className={'header-label'}>住院病案首页</span>
        <div>
          {props?.registerStatusName === '已登记' && (
            <Tag color="green">{props?.registerStatusName}</Tag>
          )}
          {props?.registerStatusName === '未登记' && (
            <Tag color="orange">{props?.registerStatusName}</Tag>
          )}
          {props?.dmrSignInDate && (
            <Tooltip
              title={`签收时间：${dayjs(props?.dmrSignInDate).format(
                'YYYY-MM-DD',
              )}`}
            >
              <Tag color="cyan">已签收</Tag>
            </Tooltip>
          )}
          {props?.viewMode ? (
            <Tag color="geekblue">只读模式</Tag>
          ) : (
            <Tag color="magenta">编辑模式</Tag>
          )}

          {hospitalName && <Tag>{hospitalName}</Tag>}
        </div>
      </div>

      <div
        id={'dmr-header-grid-layout'}
        className="grid-stack"
        style={{ margin: '0px 5px' }}
      >
        {dmrHeaderChildren}
      </div>
    </div>
  );
};

export default DMR;
