import React, { useEffect, useMemo, useRef, useState } from 'react';
import './index.less';
import { UniTable } from '@uni/components/src';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { ColumnItem, RespVO } from '@uni/commons/src/interfaces';
import {
  ReportMasterItem,
  ReportBaseInfo,
  ReportDetailItem,
  ReportItem,
  TableBaseProps,
  ReportDependenciesItem,
  ReportRelationsItem,
} from '@/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { ReportDataSize, ReportEventConstant } from '@/constants';
import {
  Button,
  Space,
  Card,
  message,
  notification,
  TableProps,
  Divider,
  Tooltip,
} from 'antd';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  BRIEF_EXPORT,
  BUNDLE_EXPORT,
  DETAIL,
  EXPORT,
  REFRESH,
} from '@/tables/constants';
import { v4 as uuidv4 } from 'uuid';
import ReportSingleLine from '@/components/single-line';
import isEmpty from 'lodash/isEmpty';
import { exportExcel } from '@uni/utils/src/excel-export';
import cloneDeep from 'lodash/cloneDeep';
import dayjs from 'dayjs';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import { useModel } from '@@/plugin-model/useModel';
import {
  dataItemToModuleActualData,
  findDataIndexInColumns,
  masterItemExtraConfig,
  reportBackgroundExport,
  reportTableGroupNameHeaderProcessor,
  tableCustomTitleProcessor,
  transformReportColumnsWithDataTypeIntOrderable,
} from '@/utils';
import { enhanceButtonEvent } from '@/utils/smartEmitter';
import merge from 'lodash/merge';
import { virtualConfig } from '@uni/components/src/table/virtual';
import { useAntdResizableHeader } from '@uni/components/src/table/resizable-column/header';
import isEqual from 'lodash/isEqual';
import { isEmptyValues } from '@uni/utils/src/utils';
import omit from 'lodash/omit';
import UniPagination, {
  externalPaginationProcessor,
} from '@uni/components/src/table/pagination';
import PivotTransformService, { hasPivotColumn } from '@/tables/pivot';
import { FileExcelOutlined } from '@ant-design/icons';

interface StatsReportReadonlyTableProps extends TableBaseProps {
  // reportDependencyItem?: ReportDependenciesItem;
  // reportRelations?: ReportRelationsItem[];

  reportGroup?: boolean;

  // 是否为group
  clickable?: boolean;
  onRowClick?: (record: any) => void;
  cellClickableProcessor?: (columns: any[]) => any[];
  // 用于 report group 下 对 关联表格发起 查询事件
  onReportSubmitClick?: (
    initialize: boolean,
    args: any,
    lineData?: any,
    columnDataIndex?: string,
    originLineData?: any,
  ) => void;

  onReportQuery?: (args: any) => void;
}

const pivotServiceInstance = new PivotTransformService();

const StatsReportReadonlyTable = (props: StatsReportReadonlyTableProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [reportLoading, setReportLoading] = useState(false);

  const [reportTableDataSource, setReportTableDataSource] = useState([]);
  const [reportTableColumns, setReportTableColumns] = useState([]);

  const [reportTableExtraColumns, setReportTableExtraColumns] = useState([]);

  const [masterItem, setMasterItem] = useState<ReportMasterItem>(
    props?.masterItem,
  );

  const [tableCardTitle, setTableCardTitle] = useState<string>('');

  const [reportQueryArgs, setReportQueryArgs] = useState({});

  const [selectedRow, setSelectedRow] = useState(undefined);
  const [selectedCell, setSelectedCell] = useState<string>(undefined);

  const { components, resizableColumns, tableWidth, resetColumns } =
    useAntdResizableHeader({
      columns: React.useMemo(() => {
        return reportTableColumns;
      }, [reportTableColumns]),
    });

  /**
   * 前端分页 start
   */
  const [frontPagination, setFrontPagination] = useState<any>({
    current: 1,
    pageSize: 30,
    pageSizeOptions: ['10', '20', '30', '50'],
    showSizeChanger: true,
    hideOnSinglePage: false,
    ...externalPaginationProcessor(true),
  });

  console.log('frontPagination', frontPagination);

  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagination) => {
    setFrontPagination({
      ...frontPagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  /**
   * 前端分页 end
   */

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      clearData: () => {
        setReportTableDataSource([]);
      },
      instantQuery: (masterId: string, args: any) => {
        setFrontPagination({
          current: 1,
          pageSize: 30,
          pageSizeOptions: ['10', '20', '30', '50'],
          showSizeChanger: true,
          hideOnSinglePage: false,
          ...externalPaginationProcessor(true),
        });
        if (isEmptyValues(reportTableColumns)) {
          reportColumnsReq(masterItem?.Id);
        }
        setReportLoading(true);
        reportDataReq(masterId, args, true);
      },
    };
  });

  useEffect(() => {
    // report item & master item
    let masterItem = cloneDeep(props?.masterItem);

    // reset
    resetState();
    // 解析ExtraConfig
    let extraConfigs = masterItemExtraConfig(masterItem?.ExtraConfig);
    if (!isEmptyValues(extraConfigs?.extraColumns)) {
      setReportTableExtraColumns(extraConfigs?.extraColumns ?? []);
    }

    setMasterItem(masterItem);
    setTableCardTitle(tableCustomTitleProcessor(masterItem, ''));
  }, [props?.masterItem]);

  // 加载数据
  useEffect(() => {
    Emitter.on(
      `${ReportEventConstant.REPORT_ITEM_QUERY_SUBMIT}_${props?.masterItem?.Id}`,
      (data) => {
        if (isEmptyValues(reportTableColumns)) {
          reportColumnsReq(masterItem?.Id);
        }
        setReportQueryArgs(data?.args);

        reportDataReq(masterItem?.Id, data?.args, false);

        props?.onReportQuery && props?.onReportQuery?.(data?.args);

        // 根据推来的数据 构建title
        setTableCardTitle(
          tableCustomTitleProcessor(masterItem, data?.selectedItem),
        );
      },
    );

    Emitter.on(
      `${ReportEventConstant.REPORT_GROUP_ROW_INITIAL_CLICK}_${props?.masterItem?.Id}`,
      (record) => {
        setSelectedRow(omit(record, ['id', 'rowId']));
      },
    );

    Emitter.on(
      `${ReportEventConstant.REPORT_GROUP_DATA_CLEAR}_${props?.masterItem?.Id}`,
      () => {
        setReportTableDataSource([]);
      },
    );

    return () => {
      Emitter.off(
        `${ReportEventConstant.REPORT_ITEM_QUERY_SUBMIT}_${props?.masterItem?.Id}`,
      );
      Emitter.off(
        `${ReportEventConstant.REPORT_GROUP_DATA_CLEAR}_${props?.masterItem?.Id}`,
      );
      Emitter.off(
        `${ReportEventConstant.REPORT_GROUP_ROW_INITIAL_CLICK}_${props?.masterItem?.Id}`,
      );
    };
  }, [masterItem, reportTableColumns, globalState, reportTableExtraColumns]);

  // 刷新数据
  useEffect(() => {
    // 刷新数据
    Emitter.on(ReportEventConstant.REPORT_REFRESH, async () => {
      setFrontPagination({
        current: 1,
        pageSize: 30,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        hideOnSinglePage: false,
        ...externalPaginationProcessor(true),
      });
      setReportLoading(true);
      reportDataReq(masterItem?.Id, reportQueryArgs, true);

      props?.onReportQuery && props?.onReportQuery?.(reportQueryArgs);
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_REFRESH);
    };
  }, [masterItem, reportQueryArgs]);

  useEffect(() => {
    // console.log('STATS_READONLY_CELL_CLICK', props);
    Emitter.on(
      `${ReportEventConstant.STATS_READONLY_CELL_CLICK}_${props?.masterItem?.Id}`,
      (data) => {
        if (data?.submitClick !== false) {
          // 全量翻译一套当前数据对应的真实显示数据出来
          props?.onReportSubmitClick &&
            props?.onReportSubmitClick(
              false,
              reportQueryArgs,
              dataItemToModuleActualData(
                data?.record,
                reportTableColumns,
                globalState?.dictData,
              ),
              data?.relationItem?.SrcColumn,
            );
        }

        // 选中行 并且选中列
        setSelectedRow(omit(data?.record, ['id', 'rowId']));
        // 选中格子
        unFillInSelectedCell(selectedCell);
        fillInSelectedCell(data?.record?.rowId, data?.relationItem?.SrcColumn);
      },
    );

    return () => {
      Emitter.off(
        `${ReportEventConstant.STATS_READONLY_CELL_CLICK}_${props?.masterItem?.Id}`,
      );
    };
  }, [
    reportQueryArgs,
    masterItem,
    props,
    reportTableColumns,
    selectedCell,
    globalState,
  ]);

  const fillInSelectedCell = (recordRowId: string, columnDataIndex: string) => {
    let dataIndexIndex = findDataIndexInColumns(
      columnDataIndex,
      reportTableColumns,
    );
    let querySelector = `tr[data-row-key="${recordRowId}"] td:nth-of-type(${dataIndexIndex})`;
    let cellItem = document.querySelector(querySelector);
    if (cellItem) {
      let currentStyle = cellItem?.getAttribute('style');
      cellItem.setAttribute(
        'style',
        currentStyle +
          'color: #1464F8 !important; font-weight: bold !important',
      );
      setSelectedCell(querySelector);
    }
  };

  const unFillInSelectedCell = (querySelector: string) => {
    if (!isEmptyValues(selectedCell)) {
      let cellItem = document.querySelector(querySelector);
      if (cellItem) {
        cellItem.setAttribute('style', 'text-align: right;');
      }
    }
  };

  // 处理导出数据的通用函数
  const handleExportAction = (payload) => {
    message.success('导出中');

    // 后端导出
    if (masterItem?.EnableBackgroundExport) {
      reportBackgroundExport(
        tableCardTitle ?? masterItem?.Title,
        masterItem?.Id,
        undefined,
        reportQueryArgs,
      );
      return;
    }

    let exportName = `${tableCardTitle ?? masterItem?.Title}-${dayjs().format(
      'YYYYMMDD_HHmmss',
    )}`;

    const canExportColumns = reportTableColumns?.filter(
      (columnItem) =>
        columnItem.className?.indexOf('exportable') !== -1 &&
        columnItem.valueType !== 'option' &&
        columnItem.dataIndex !== 'operation' &&
        columnItem.dataIndex !== 'directTo',
    );
    if (!isEmpty(canExportColumns)) {
      exportExcel(
        canExportColumns.slice() as any[],
        exportExcelDictionaryModuleProcessor(
          canExportColumns,
          cloneDeep(reportTableDataSource.slice()),
          globalState?.dictData,
        ),
        exportName,
        [],
      );
      message.success('导出成功');
    }
  };

  // 导出结果 - 监听事件
  useEffect(() => {
    // 只监听带ID的导出事件，避免全局事件冲突
    if (props?.masterItem?.Id) {
      Emitter.on(
        `${ReportEventConstant.REPORT_EXPORT}_${props?.masterItem?.Id}`,
        handleExportAction,
      );
    }

    return () => {
      // 卸载时取消特定ID事件的监听
      if (props?.masterItem?.Id) {
        Emitter.off(
          `${ReportEventConstant.REPORT_EXPORT}_${props?.masterItem?.Id}`,
        );
      }
    };
  }, [
    reportTableColumns,
    reportTableDataSource,
    masterItem,
    globalState?.dictData,
  ]);

  useEffect(() => {
    processOperationBtns();
  }, [masterItem, reportTableDataSource]);

  const resetState = () => {
    //reset
    setReportTableDataSource([]);
    setReportTableColumns([]);
    setFrontPagination({
      current: 1,
      pageSize: 30,
      pageSizeOptions: ['10', '20', '30', '50'],
      showSizeChanger: true,
      hideOnSinglePage: false,
      ...externalPaginationProcessor(true),
    });
  };

  const processOperationBtns = () => {
    let btns = [];

    if (masterItem?.Id) {
      btns.push(DETAIL());

      btns.push(<Divider type="vertical" />);

      if (masterItem?.EnableExport === true) {
        // 获取原始的导出按钮
        const originalExport = EXPORT(reportTableDataSource?.length === 0);
        console.log('originalExport', masterItem);
        // 使用enhanceButtonEvent增强按钮，传入事件名称和表格ID
        const enhancedExport = enhanceButtonEvent(
          originalExport,
          ReportEventConstant.REPORT_EXPORT,
          masterItem?.Id,
        );

        btns.push(enhancedExport);

        if (props?.reportGroup === true) {
          btns.push(
            BUNDLE_EXPORT(reportTableDataSource?.length === 0, {
              args: reportQueryArgs,
            }),
          );
        }
      }

      if (masterItem?.EnableExportBrief === true) {
        if (props?.reportGroup === true) {
          btns.push(
            BRIEF_EXPORT(reportTableDataSource?.length === 0, {
              args: reportQueryArgs,
            }),
          );
        }
      }
    }

    return btns;
  };

  // columns
  const {
    data: reportOriginColumns,
    loading: reportColumnsLoading,
    run: reportColumnsReq,
  } = useRequest(
    (id) => {
      return uniCommonService('Api/Report/Report/GetReportDataTablesColumns', {
        params: {
          ReportSettingMasterId: id,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<ColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let tableColumns = tableColumnBaseProcessor(
            [],
            transformReportColumnsWithDataTypeIntOrderable(
              masterItem?.ReportMode,
              response?.data,
            ),
          );
          if (props?.cellClickableProcessor) {
            tableColumns = props?.cellClickableProcessor(tableColumns);
          }

          let transformedTableColumns = reportTableExtraColumns.concat(
            reportTableGroupNameHeaderProcessor(tableColumns),
          );

          if (hasPivotColumn(transformedTableColumns)) {
            pivotServiceInstance.setTableColumns(transformedTableColumns);
            setReportTableColumns([]);
          } else {
            setReportTableColumns(transformedTableColumns);
          }
        } else {
          setReportTableColumns([]);
        }
      },
    },
  );

  const reportDataProcessor = async (data: any[]) => {
    // if(masterItem?.EnableValidate) {
    //   let syncValidateResponse : RespVO<any[]> = await syncValidateReport(
    //     reportItem?.ReportSettingMasterId,
    //     reportItem?.ReportBaseId,
    //   );
    //
    //   if(syncValidateResponse?.code === 0 && syncValidateResponse?.statusCode === 200) {
    //     if(syncValidateResponse?.data?.length !== 0) {
    //       message.error("报表数据校验出现错误");
    //       setReportLoading(false);
    //       return;
    //     }
    //   } else {
    //     message.error("报表数据校验失败，请稍后重试");
    //     setReportLoading(false);
    //     return;
    //   }
    // }

    let tableData = data?.map((item) => {
      return {
        rowId: uuidv4(),
        ...item,
      };
    });
    setReportTableDataSource(tableData);

    if (pivotServiceInstance?.hasPivotColumns === true) {
      let pivotData = pivotServiceInstance.pivotColumnsDataTransformer(
        tableData,
        [],
      );

      setReportTableColumns(pivotData?.columns);
      setReportTableDataSource(pivotData?.dataSources ?? []);
    }

    setReportLoading(false);
  };

  const { loading: reportDataLoading, run: reportDataReq } = useRequest(
    (masterId, reportArgs, reload) => {
      let data = {
        ReportSettingMasterId: masterId,
        ReportArgs: reportArgs,
        Reload: reload || false,
      };

      return uniCommonService('Api/Report/Report/Query', {
        method: 'POST',
        data: data,
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          reportDataProcessor(response?.data || []);
        } else {
          setReportTableDataSource([]);
          setReportLoading(false);
        }

        return response?.data ?? [];
      },
      onSuccess: (responseData, params) => {
        console.log('onSuccess', responseData, params);
        props?.onReportSubmitClick &&
          props?.onReportSubmitClick(
            true,
            params?.at(1),
            dataItemToModuleActualData(
              responseData?.at(0),
              reportTableColumns,
              globalState?.dictData,
            ),
            '',
            responseData?.at(0),
          );
      },
    },
  );

  // 不用resizeheader就不需要再减数字
  const tableY =
    document.getElementById('report-content-container')?.offsetHeight -
    ((document.querySelector('#report-content-container .ant-card-head') as any)
      ?.offsetHeight || 0) -
    (document.getElementsByClassName('ant-table-thead')?.[0]?.clientHeight ||
      0) -
    (document.getElementById('query-header-container')?.offsetHeight || 0) -
    32 -
    16 -
    28 -
    10 -
    2 -
    2;

  return (
    <div
      id={'stats-table-readonly-container'}
      className={'stats-table-readonly-container'}
    >
      {false && masterItem?.DataSize === ReportDataSize.SingleLine ? (
        <Card title={tableCardTitle ?? masterItem?.Title}>
          <ReportSingleLine
            masterItem={masterItem}
            reportItem={undefined}
            columns={reportTableColumns}
            tableDataSource={reportTableDataSource}
          />
        </Card>
      ) : (
        <>
          <Card
            title={tableCardTitle ?? masterItem?.Title}
            id={'report-content-container'}
            className={'report-content-container'}
            extra={
              <>
                <Space>{processOperationBtns()}</Space>
              </>
            }
          >
            <UniTable
              id={'report-table'}
              rowKey={'rowId'}
              scroll={{
                x: 'max-content',
                y: Math.max(tableY ?? 200, 200),
              }}
              widthDetectAfterDictionary
              rowClassName={(record, index) => {
                return isEqual(omit(record, ['id', 'rowId']), selectedRow)
                  ? 'row-selected'
                  : '';
              }}
              forceColumnsUpdate={true}
              columns={reportTableColumns} // resizableColumns
              dataSource={reportTableDataSource}
              clickable={props?.clickable ?? false}
              components={components}
              bordered={true}
              loading={reportDataLoading || reportLoading}
              pagination={frontPagination}
              onChange={frontTableOnChange}
              dictionaryData={globalState?.dictData}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (props?.clickable) {
                      props?.onReportSubmitClick &&
                        props?.onReportSubmitClick(
                          false,
                          reportQueryArgs,
                          dataItemToModuleActualData(
                            record,
                            reportTableColumns,
                            globalState?.dictData,
                          ),
                          '',
                        );
                      setSelectedRow(omit(record, ['id', 'rowId']));
                      unFillInSelectedCell(selectedCell);
                      setSelectedCell(undefined);
                    }
                  },
                };
              }}
            />
          </Card>
        </>
      )}
    </div>
  );
};

export default StatsReportReadonlyTable;
