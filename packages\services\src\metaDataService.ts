import request, { extend } from 'umi-request';
import { errorHandler } from './config';
import { appendPortWhenPortMissing } from './processor';

const metaDataDomain =
  appendPortWhenPortMissing(
    (window as any).externalConfig?.domain?.dmrMetaDataDomain,
  ) ??
  process.env.dmrMetaDataDomain ??
  'http://172.16.3.152:5181';

export const dmrMetaDataService = (module: string) => {
  return metaDataService(module, 'Dmr');
};

export const dmrModulesMetaDataService = (modules: string[]) => {
  return modulesMetaDataService(modules, 'Dmr');
};

export const insurMetaDataService = (module: string) => {
  return metaDataService(module, 'Insur');
};

export const insurModulesMetaDataService = (modules: string[]) => {
  return modulesMetaDataService(modules, 'Insur');
};

export const metaDataService = (module: string, moduleGroup: string) => {
  let params = {
    module: module,
  };
  if (moduleGroup) {
    params['moduleGroup'] = moduleGroup;
  }

  let requestParams = {
    method: 'GET',
    params: params,
  };
  return request(
    `${metaDataDomain}/api/MetaData/DictionaryModule/GetDictionaryModule`,
    requestParams,
  );
};

export const modulesMetaDataService = (
  modules: string[],
  moduleGroup: string,
) => {
  // console.log('modules, moduleGroup', modules, moduleGroup);
  let params = {
    modules: modules,
  };
  if (moduleGroup) {
    params['moduleGroup'] = moduleGroup;
  }

  // drg做处理
  let requestParams = {
    method: 'POST',
    data: params,
  };
  return request(
    `${metaDataDomain}/api/MetaData/DictionaryModule/GetDictionaryModules`,
    requestParams,
  );
};

export const metaDataServiceWithoutCache = (module, moduleGroup) => {
  let params = {
    module: module,
    useCache: false,
  };
  if (moduleGroup) {
    params['moduleGroup'] = moduleGroup;
  }

  let requestParams = {
    method: 'GET',
    params: params,
  };
  return request(
    `${metaDataDomain}/api/MetaData/DictionaryModule/GetDictionaryModule`,
    requestParams,
  );
};
