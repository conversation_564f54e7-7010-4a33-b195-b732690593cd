import './index.less';
import { BatchItem, BatchMasterSysItem } from '@/pages/review/interface';
import { useModel } from 'umi';
import SumReviewTable from './sumReviewTable';
import SumEncodeTable from './sumEncodeTable';
import SumAuditeeTable from './sumAuditeeTable';

interface ReviewPersonSummaryProps {
  activeKey?: string;
  onTableRowSelect: (record: any, type: string) => void;
  sysMasterItem?: BatchMasterSysItem;
  searchParams?: any;
}

const ReviewPersonSummary = (props: ReviewPersonSummaryProps) => {
  const { searchParams, activeKey, onTableRowSelect, sysMasterItem } = props;
  const { globalState } = useModel('@@qiankunStateFromMaster');

  return (
    <div className="review-person-summary-container">
      <SumReviewTable
        activeKey={activeKey}
        onTableRowSelect={onTableRowSelect}
        sysMasterItem={sysMasterItem}
        searchParams={searchParams}
      />
      <SumEncodeTable
        activeKey={activeKey}
        onTableRowSelect={onTableRowSelect}
        sysMasterItem={sysMasterItem}
        searchParams={searchParams}
      />
      {!['Coder', 'UnSpec']?.includes(sysMasterItem?.Master?.RevieweeType) && (
        <SumAuditeeTable
          activeKey={activeKey}
          onTableRowSelect={onTableRowSelect}
          sysMasterItem={sysMasterItem}
          searchParams={searchParams}
        />
      )}
    </div>
  );
};

export default ReviewPersonSummary;
