const wikiShow = (window as any).externalConfig?.['common']?.wikiShow ?? false;
const wikiInsurShow =
  (window as any).externalConfig?.['common']?.wikiInsurShow ?? false;
const personalMessageShow =
  (window as any).externalConfig?.['common']?.personalMessageShow ?? false;

const basicAccess = {
  '/': true,
  '/login': true,
  '/sso': true,
  '/main': true,
  '/external/*': true,
};

const allAccess = {
  '/systemConfiguration': true,

  // permission
  '/systemConfiguration/permissions': true,
  '/systemConfiguration/permissions/menu': true,
  '/systemConfiguration/permissions/users': true,
  '/systemConfiguration/permissions/roles': true,

  // 首页登记布局
  '/systemConfiguration/layouts': true,
  '/systemConfiguration/layouts/index': true,
  '/systemConfiguration/layouts/enrollment': true,

  // dmr首页配置
  '/systemConfiguration/base': true,
  '/systemConfiguration/base/dictionary': true,
  '/systemConfiguration/base/dmrColumnDictMatch': true,
  '/systemConfiguration/base/icde': true,
  '/systemConfiguration/base/operation': true,
  '/systemConfiguration/base/category': true,
  '/systemConfiguration/base/missing': true,
  '/systemConfiguration/base/department': true,
  '/systemConfiguration/base/icdeCategory': true,
  '/systemConfiguration/base/operCategory': true,
  '/systemConfiguration/base/operSet': true,

  // dmr接口对照
  '/systemConfiguration/path': true,
  '/systemConfiguration/path/dictionary': true,
  '/systemConfiguration/path/icde': true,
  '/systemConfiguration/path/operation': true,
  '/systemConfiguration/path/department': true,
  '/systemConfiguration/path/ward': true,
  '/systemConfiguration/path/dynHierarchy': true,

  // insur结算配置(copy from dmr首页)
  '/systemConfiguration/insur': true,
  '/systemConfiguration/insur/dictionary': true,
  '/systemConfiguration/insur/insurColumnDictMatch': true,
  '/systemConfiguration/insur/icde': true,
  '/systemConfiguration/insur/operation': true,
  '/systemConfiguration/insur/department': true,
  '/systemConfiguration/insur/missing': true,

  // hqms国考配置(copy from dmr首页)
  '/systemConfiguration/hqms': true,
  '/systemConfiguration/hqms/dictionary': true,
  '/systemConfiguration/hqms/icde': true,
  '/systemConfiguration/hqms/operation': true,
  '/systemConfiguration/hqms/department': true,
  '/systemConfiguration/hqms/missing': true,

  // wt卫统配置(copy from dmr首页)
  '/systemConfiguration/wt': true,
  '/systemConfiguration/wt/dictionary': true,
  '/systemConfiguration/wt/icde': true,
  '/systemConfiguration/wt/operation': true,
  '/systemConfiguration/wt/department': true,

  // 对出
  '/systemConfiguration/escalate': true,
  '/systemConfiguration/escalate/dictionary': true,
  '/systemConfiguration/escalate/icde': true,
  '/systemConfiguration/escalate/operation': true,
  '/systemConfiguration/escalate/department': true,

  // 医疗机构
  '/systemConfiguration/institution': true,
  '/systemConfiguration/institution/hospital': true,
  '/systemConfiguration/institution/department': true,
  '/systemConfiguration/institution/ward': true,
  '/systemConfiguration/institution/employees': true,
  '/systemConfiguration/institution/hospEmployee': true,
  '/systemConfiguration/institution/majorPerfDept': true,
  '/systemConfiguration/institution/perfDept': true,
  '/systemConfiguration/institution/hierarchyPerfDept': true,

  '/systemConfiguration/reportCliDeptSettings': true,
  // 日历
  '/systemConfiguration/calendar': true,

  // 医保系统配置
  '/insurConfiguration': true,
  // 用户 & 权限
  '/insurConfiguration/permissions': true,
  '/insurConfiguration/permissions/menu': true,
  '/insurConfiguration/permissions/users': true,
  '/insurConfiguration/permissions/roles': true,
  // 结算清单配置
  '/insurConfiguration/insur': true,
  '/insurConfiguration/insur/dictionary': true,
  '/insurConfiguration/insur/insurColumnDictMatch': true,
  '/insurConfiguration/insur/icde': true,
  '/insurConfiguration/insur/icdeReference': true, // 对照
  '/insurConfiguration/insur/operation': true,
  '/insurConfiguration/insur/operationReference': true, // 对照
  '/insurConfiguration/insur/department': true,
  '/insurConfiguration/insur/missing': true,
  // 医疗机构人员
  '/insurConfiguration/institution': true,
  '/insurConfiguration/institution/hospital': true,
  '/insurConfiguration/institution/department': true,
  '/insurConfiguration/institution/ward': true,
  '/insurConfiguration/institution/employees': true,
  '/insurConfiguration/institution/hierarchyPerfDept': true,
  '/insurConfiguration/institution/majorPerfDept': true,
  '/insurConfiguration/institution/perfDept': true,
  // 其他各种
  // 医嘱目录管理
  '/insurConfiguration/advsManagement': true,
  // 收费目录管理
  '/insurConfiguration/medChrgitmsManagement': true,
  // DRG支付标准管理
  '/insurConfiguration/drgPayStandard': true,
  // DIP支付标准管理
  '/insurConfiguration/dipPayStandard': true,
  // 在院病人信息字典
  '/insurConfiguration/inHospPatientInfoDict': true,
  // 异常病例监控规则
  '/insurConfiguration/abnormalCaseRules': true,

  // 子系统
  '/dmr': true,
  '/dmr/index': true,
  '/dmr/index/fullscreen': true,
  '/dmr/management': true,
  '/dmr/coderManagement': true,
  '/dmr/search': true,
  '/dmr/statistic': true,
  '/dmr/medicalRecord': true,
  '/dmr/roomingInManagement': true,
  '/dmr/configuration': true,
  '/dmr/revision': true,
  '/dmr/history': true,
  '/dmr/examine': true,
  '/dmr/examine/reviewer': true,
  '/dmr/examine/auditee': true,
  '/dmr/examine/management': true,
  '/dmr/examine/report': true,
  '/dmr/examine/highlight/*': true,

  '/chs': true,
  '/chs/main': true,
  '/chs/main/index': true,
  '/chs/main/index/fullscreen': true,

  '/chs/main/monitor': true,
  '/chs/main/management': true,
  '/chs/main/settleInfo': true,
  '/chs/main/match': true,

  '/chs/main/qcAnalysis': true,
  '/chs/main/qcAnalysis/hosp': true,
  '/chs/main/qcAnalysis/dept': true,

  '/chs/main/details': true,
  '/chs/main/details/dept': true,

  '/chs/main/encode': true,
  '/chs/main/encode/details': true,
  '/chs/main/encode/diff': true,

  // 特病单议管理 页面 **默认关闭 上海胸科默认开启**
  '/chs/main/specialDisease': false,
  '/chs/main/specialDisease/discussion': false,
  '/chs/main/specialDisease/reportStats': false,
  '/chs/main/specialDisease/importRecord': false,

  // DIP
  '/chs/dip': true,
  '/chs/dip/analysis': true,
  '/chs/dip/analysis/hosp': true,
  '/chs/dip/analysis/majorPerfDept': false,
  '/chs/dip/analysis/dept': true,
  '/chs/dip/analysis/group': true,
  '/chs/dip/analysis/pay': true,
  '/chs/dip/analysis/medTeam': true,
  '/chs/dip/exportDetail': true, // DIP 分组明细
  '/chs/dip/important': true,
  '/chs/dip/important/hosp': true,
  '/chs/dip/important/majorPerfDept': false,
  '/chs/dip/important/dept': true,
  '/chs/dip/important/medTeam': true,
  '/chs/dip/important/variableConditions': true,
  // 在院病人监控
  '/chs/dip/warning': true,
  '/chs/dip/warning/hosp': true,
  '/chs/dip/warning/dept': true,
  // 患者明细
  '/chs/dip/warning/patientInfo': true,
  // 支付审核规则配置
  // '/chs/dip/payRuleSettings': true,

  '/chs/analysis': true,
  // 预支付分析
  '/chs/analysis/pre': false,
  '/chs/analysis/pre/index': false,
  '/chs/analysis/pre/hosp': false,
  '/chs/analysis/pre/dept': false,
  '/chs/analysis/pre/disease': false,
  // '/chs/analysis/fee': true,
  // 支付监控分析
  '/chs/analysis/drg': true,
  '/chs/analysis/drg/hosp': true,
  '/chs/analysis/drg/majorPerfDept': true,
  '/chs/analysis/drg/dept': true,
  '/chs/analysis/drg/medTeam': true,
  '/chs/analysis/drg/group': true,
  '/chs/analysis/drg/pay': true,
  // 支付明细查询
  '/chs/analysis/payDetail': true,
  // 在院病人监控
  '/chs/analysis/warning': true,
  '/chs/analysis/warning/hosp': true,
  '/chs/analysis/warning/dept': true,
  // 患者明细
  '/chs/analysis/warning/patientInfo': true,
  // 重点病例监控
  '/chs/analysis/important': true,
  '/chs/analysis/important/hosp': true,
  '/chs/analysis/important/majorPerfDept': false,
  '/chs/analysis/important/dept': true,
  '/chs/analysis/important/medTeam': true,
  // // 重点病例监控 / 高差异度病种监控
  '/chs/analysis/important/variableConditions': true,

  '/chs/analysis/cardInfo': true,

  // his report
  '/chs/analysis/report': true,
  '/chs/analysis/highlight': true,
  '/chs/main/report': true,
  '/chs/main/highlight': true,
  '/chs/dip/report': true,
  '/chs/dip/highlight': true,

  '/qualityControl': true,
  // '/qualityControl/index': true,
  '/qualityControl/main': true,
  '/qualityControl/main/analysis': true,
  '/qualityControl/main/analysis/hosp': true,
  '/qualityControl/main/analysis/dept': true,
  '/qualityControl/main/analysis/match': true,
  '/qualityControl/main/rule': true,
  '/qualityControl/main/customRule': true, // 自定义质控规则 默认关闭 shjy默认开启
  '/qualityControl/main/details': true,
  '/qualityControl/main/details/type': true,
  '/qualityControl/main/details/casebook': true,
  '/qualityControl/main/efficiency': true, // 编码能效 默认关闭 shjy默认开启
  '/qualityControl/main/efficiency/codeValueQuantification': true, // 工作效率管理 默认关闭 shjy默认开启
  '/qualityControl/main/efficiency/codeWorkerLoad': true, // 编码价值量化/编码价值洞察 默认关闭 shjy默认开启

  '/qualityControl/doctor': true,
  '/qualityControl/doctor/analysis': true,
  '/qualityControl/doctor/analysis/hosp': true,
  '/qualityControl/doctor/analysis/dept': true,
  '/qualityControl/doctor/details': true,
  '/qualityControl/doctor/details/casebook': true,
  '/qualityControl/doctor/details/type': true,

  // 编码价值量化 **默认关闭 shjy默认开启**
  '/energyEfficientManagement': true,
  '/energyEfficientManagement/codeValueQuantification': true,
  '/energyEfficientManagement/codeWorkerLoad': true,

  '/uniHqms': true,
  // 新  国考  版本 不要这个
  // '/uniHqms/score': true,
  '/uniHqms/index': true,
  '/uniHqms/oper': true,
  '/uniHqms/quality': true,
  '/uniHqms/sd': true,
  '/uniHqms/tcm': true,
  '/uniHqms/cmi': true,
  '/uniHqms/oper/hosp': true,
  '/uniHqms/oper/dept': true,
  '/uniHqms/oper/medTeam': true,
  '/uniHqms/quality/hosp': true,
  '/uniHqms/quality/dept': true,
  '/uniHqms/quality/medTeam': true,
  //
  '/uniHqms/cmi/hospLevelCmi': true,
  '/uniHqms/cmi/deptLevelCmi': true,
  '/uniHqms/cmi/medTeamLevelCmi': true,
  //
  '/uniHqms/operComplicationComposition/hosp': true,
  '/uniHqms/operComplicationComposition/dept': true,
  '/uniHqms/operComplicationComposition/medTeam': true,
  '/uniHqms/sd/hosp': true,
  '/uniHqms/sd/dept': true,
  '/uniHqms/sd/medTeam': true,
  '/uniHqms/tcm/hosp': true,
  '/uniHqms/tcm/dept': true,
  '/uniHqms/tcm/medTeam': true,

  '/grade': true,
  '/grade/index': true,
  '/grade/quality': true,
  '/grade/quality/hosp': true,
  '/grade/quality/dept': true,
  '/grade/quality/medTeam': true,
  '/grade/sd': true,
  '/grade/sd/hosp': true,
  '/grade/sd/dept': true,
  '/grade/sd/medTeam': true,
  '/grade/serviceability': true,
  '/grade/serviceability/hosp': true,
  '/grade/serviceability/dept': true,
  '/grade/serviceability/medTeam': true,
  '/grade/complication': true,
  '/grade/complication/hosp': true,
  '/grade/complication/dept': true,
  '/grade/complication/medTeam': true,

  '/operationalDataManagement': true,
  '/operationalDataManagement/workLoad': true,
  '/operationalDataManagement/workLoad/medicalTechWorkload': true,
  '/operationalDataManagement/workLoad/report/workLoadItem': true,
  '/operationalDataManagement/workLoad/hospWorkloadItemCheck': true,
  '/operationalDataManagement/workLoad/workloadItemCheck': true,
  '/operationalDataManagement/in': true,
  '/operationalDataManagement/in/byDay': true,
  '/operationalDataManagement/in/byDayWithWard': true,
  '/operationalDataManagement/in/byDept': true,
  '/operationalDataManagement/in/byWard': true,
  '/operationalDataManagement/in/hierarchyBed': true,
  '/operationalDataManagement/in/proofread': true,
  // 住院动态日报校对 txey目前在用 等稳定了再打开
  '/operationalDataManagement/in/dailyProofread': false,

  '/operationalDataManagement/obs': true,
  '/operationalDataManagement/obs/byDay': true,
  '/operationalDataManagement/obs/byDept': true,
  '/operationalDataManagement/obs/hierarchyBed': true,
  '/operationalDataManagement/out': true,
  '/operationalDataManagement/out/byDay': true,
  '/operationalDataManagement/out/byDept': true,
  '/operationalDataManagement/outDoctor': true,
  '/operationalDataManagement/outDoctor/byDay': true,
  '/operationalDataManagement/outDoctor/byDept': true,
  // XX动态管理
  '/operationalDataManagement/inHospManagement': true,
  '/operationalDataManagement/outPatientManagement': true,

  '/tracer': true,
  '/tracer/traceRecordList': true,
  '/tracer/ward': true,
  '/tracer/ward/search': true,
  '/tracer/ward/signin': true,
  '/tracer/mrRoom': true,
  '/tracer/mrRoom/search': true,
  '/tracer/mrRoom/signin': true,
  '/tracer/mrRoom/statistic': true,
  '/tracer/mrRoom/statistic/bySignDate': true,
  '/tracer/mrRoom/statistic/byOutDate': true,

  '/tracer/archive': true,
  '/tracer/archive/search': true,
  '/tracer/archive/register': true,
  // '/tracer/seal': true,
  // '/tracer/seal/search': true,
  // '/tracer/seal/register': true,
  '/tracer/borrow': true,
  '/tracer/borrow/search': true,
  '/tracer/borrow/borrowRegister': true,
  '/tracer/borrow/returnRegister': true,
  '/tracer/borrow/askForReturnSearch': true,
  '/tracer/print': true,
  '/tracer/print/search': true,
  '/tracer/print/register': true,
  // '/tracer/remind': true,
  // '/tracer/remind/search': true,
  // '/tracer/remind/register': true,
  '/tracer/mrRoomSignInAnalysis': true,
  '/tracer/askForPaySearch': true,
  '/tracer/borrow/borrowTasks': true,
  '/tracer/borrow/userBorrowTasks': true,
  // 病案室出库 **默认admin开启 若不给用户使用则在菜单管理关闭**
  '/tracer/dmrSignOut': true,
  '/tracer/dmrSignOut/search': true,
  '/tracer/dmrSignOut/discharge': true,

  // 报表
  '/report': true,
  '/report/hospital': true,
  '/report/hqms': true,
  '/report/wt': true,
  '/report/public': true,

  '/statsAnalysis': true,
  '/statsAnalysis/combineQuery': true,
  '/statsAnalysis/report/hospital': true,
  '/statsAnalysis/report/public': true,
  '/statsAnalysis/surgerySequence': true,
  '/statsAnalysis/diseaseSequence': true,
  '/statsAnalysis/customDiseaseStats': true,
  // 简单查询 **四川省人民定制页面 默认关闭**
  '/statsAnalysis/simpleQuery': false,

  //
  '/drgHospDecisionSupport': true,
  '/drgHospDecisionSupport/cmi': true,

  // 国考 数据上传 & 数据管理
  '/drgHospDecisionSupport/hqmsCardImport': true,
  '/drgHospDecisionSupport/hqmsDataManagement': true,

  '/drgHospDecisionSupport/hospLevel/Cmi': true,
  '/drgHospDecisionSupport/hospLevel/diseaseType': true,
  '/drgHospDecisionSupport/hospLevel/groupType': true,
  '/drgHospDecisionSupport/hospLevel/operRate': true,
  '/drgHospDecisionSupport/hospLevel/SdComposition': true,

  // 疑难病例
  '/drgHospDecisionSupport/difficultCases': true,
  '/drgHospDecisionSupport/difficultCases/hosp': true,
  '/drgHospDecisionSupport/difficultCases/dept': true,
  '/drgHospDecisionSupport/difficultCases/medTeam': true,

  // 外科能力
  '/drgHospDecisionSupport/surgicalAbility': true,
  '/drgHospDecisionSupport/surgicalAbility/hosp': true,
  '/drgHospDecisionSupport/surgicalAbility/dept': true,
  '/drgHospDecisionSupport/surgicalAbility/medTeam': true,

  // 重点监控
  '/drgHospDecisionSupport/sdComposition': true,
  '/drgHospDecisionSupport/sdComposition/hosp': true,
  '/drgHospDecisionSupport/sdComposition/dept': true,
  '/drgHospDecisionSupport/sdComposition/medTeam': true,

  // 医疗质量
  '/drgHospDecisionSupport/medicalQuality': true,
  '/drgHospDecisionSupport/medicalQuality/hosp': true,
  '/drgHospDecisionSupport/medicalQuality/dept': true,
  '/drgHospDecisionSupport/medicalQuality/medTeam': true,

  '/drgHospDecisionSupport/detail': true,
  '/drgHospDecisionSupport/detail/hospDrgs': true,
  '/drgHospDecisionSupport/detail/hospOper': true,
  '/drgHospDecisionSupport/detail/hospSd': true,
  '/drgHospDecisionSupport/oper': true,
  '/drgHospDecisionSupport/sd': true,

  '/drgHospDecisionSupport/deptLevel/Cmi': true,
  '/drgHospDecisionSupport/deptLevel/diseaseType': true,
  '/drgHospDecisionSupport/deptLevel/groupType': true,
  '/drgHospDecisionSupport/deptLevel/operRate': true,
  '/drgHospDecisionSupport/deptLevel/SdComposition': true,

  '/drgHospDecisionSupport/medTeamLevel/Cmi': true,
  '/drgHospDecisionSupport/majoePerfDept/Cmi': true,
  '/drgHospDecisionSupport/medTeamLevel/diseaseType': true,
  '/drgHospDecisionSupport/medTeamLevel/groupType': true,

  //
  '/import': true,
  '/import/pullDmrCards': true,
  '/import/pullDmrSingleCard': true,
  '/import/pullDmrCardFees': true,
  '/import/interfaceSetUpload': true,
  '/import/interfaceSetUploadCenterSettle': true,
  // 维护
  '/reportSys': true,
  '/reportSys/reportSettings': true,
  '/reportSys/reportTree': true,
  // 统计科室设置 new
  '/reportSys/statDeptsSetting': true,
  '/reportSys/dataManagement': true,
  '/reportSys/dataFlow': true,
  '/reportSys/dataReload': true,
  '/reportSys/insurDataFlow': true,
  '/reportSys/selfDefinedReport': true,
  '/reportSys/backendReport': true,
  '/reportSys/InterfaceSet': true,
  '/reportSys/deptSettings': true,
  '/reportSys/globalConfiguration': true,
  '/reportSys/globalConfiguration/moduleMenu': true,
  '/reportSys/globalConfiguration/menuOrder': true,
  '/reportSys/systemInfo': true,
  '/reportSys/settingColumnDefs': true,
  '/reportSys/rulesScore': true,
  '/reportSys/jsonExprRules': true,
  '/reportSys/qualityControlRuleConfiguration': false,
  // 前置分组结果查询
  '/reportSys/externalCalcRecord': true,
  '/reportSys/_health': true,
  // 支付审核规则配置
  '/reportSys/insurPayRuleSettings': true,
  '/reportSys/clientApiKey': true,
  '/reportSys/recurringJob': true,
  // 人工质控业务配置
  '/reportSys/qualityExamine': true,
  // 字典库汇总
  '/reportSys/dictModule': true,
  // kpi配置
  '/reportSys/kpiSettings': true,

  // 医保基金监控
  '/medicalInsuranceFundMonitor': false,
  '/medicalInsuranceFundMonitor/appeal': true,
  '/medicalInsuranceFundMonitor/rule': true,
  '/medicalInsuranceFundMonitor/rule/medicine': true,
  '/medicalInsuranceFundMonitor/rule/medicine/ruleControl': true,
  '/medicalInsuranceFundMonitor/rule/service': true,
  '/medicalInsuranceFundMonitor/rule/service/ruleControl': true,
  '/medicalInsuranceFundMonitor/rule/material': true,
  '/medicalInsuranceFundMonitor/rule/material/ruleControl': true,
  '/medicalInsuranceFundMonitor/review/dashboard/prior': true,
  '/medicalInsuranceFundMonitor/review/dashboard/during': true,
  '/medicalInsuranceFundMonitor/review/dashboard/post': true,
  '/medicalInsuranceFundMonitor/review/dashboard/discharge': true,

  // DRG院内版
  'Api/Drgs/DrgStats/UniDrgsProtoCoreRedirect': true,

  '/personalMessages': personalMessageShow,
  '/personalMessages/index': personalMessageShow,

  '/wiki': wikiShow,
  '/wiki/insur': wikiShow && wikiInsurShow,
  '/wiki/insur/index': wikiShow && wikiInsurShow,
  '/wiki/insur/ccMcc': wikiShow && wikiInsurShow,
  '/wiki/insur/mcc': wikiShow && wikiInsurShow,
  '/wiki/insur/cc': wikiShow && wikiInsurShow,
  '/wiki/insur/icde': wikiShow && wikiInsurShow,
  '/wiki/insur/oper': wikiShow && wikiInsurShow,
  '/wiki/dmrExternalCalc': wikiShow && wikiInsurShow,

  '/wiki/dip': wikiShow,
  '/wiki/dip/index': wikiShow,
  '/wiki/dip/icde': wikiShow,
  '/wiki/dip/oper': wikiShow,

  '/wiki/dmr': wikiShow,
  '/wiki/dmr/icde': wikiShow,
  '/wiki/dmr/oper': wikiShow,

  // 独立质控
  '/qcStandalone': true,
  '/qcStandalone/details': true,
  '/qcStandalone/details/type': true,
  '/qcStandalone/details/casebook': true,
  '/qcStandalone/details/admin/type': true,
  '/qcStandalone/details/admin/casebook': true,
  '/qcStandalone/review': true,
  '/qcStandalone/review/detail': true,
  '/qcStandalone/review/progress': true,
  '/qcStandalone/review/closed': true,
  '/qcStandalone/review/invalid': true,
  '/qcStandalone/rules/config': true,
  '/qcStandalone/import': true,
  '/qcStandalone/import/pullDmrCards': true,
  '/qcStandalone/import/pullDmrSingleCard': true,
};

export default function (initialState: any): any {
  // TODO 建议还是扩展 到路由内部的某些权限
  // TODO 这里有可能会出现 未定义即可访问 需要在config中定义access: {strictMode: true}

  let access = {
    ...basicAccess,
  };

  if (initialState?.userInfo?.Roles?.includes('Admin')) {
    access = {
      ...access,
      ...allAccess,
    };
  } else {
    access = {
      ...access,
      ...(initialState?.userInfo?.Preferences?.Menu || {}),
    };

    // 在all access 中 但是不在 access 中的设默认设定为false
    // 并且一定以 / 开头 表示为 前端页面
    // 表示的是新页面未分配权限
    let notInMenusButInAllAccess = {};
    Object.keys(allAccess)?.forEach((key) => {
      if (key.startsWith('/')) {
        if (access?.[key] === undefined || access?.[key] === null) {
          notInMenusButInAllAccess[key] = false;
        }
      }
    });

    access = {
      ...access,
      ...notInMenusButInAllAccess,
    };
  }

  return access;
}
