export const PreCheckSvg = () => (
  <svg
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 48 48"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 36L8.00461 28.0426C8.00551 27.4906 8.45313 27.0432 9.00519 27.0426C12.3391 27.0426 15.6731 27.0426 19.0071 27.0426C19.9286 27.0426 19.9237 26.2252 19.9237 24.2792C19.9237 22.3332 15.0221 20.6941 15.0221 13.8528C15.0221 7.01151 20.0999 5 24.32 5C28.5401 5 33.1366 7.01151 33.1366 13.8528C33.1366 20.6941 28.2607 21.7818 28.2607 24.2792C28.2607 26.7765 28.2607 27.0426 29.0413 27.0426C32.3609 27.0426 35.6806 27.0426 39.0003 27.0426C39.5525 27.0426 40.0003 27.4904 40.0003 28.0426V36H8Z"
      fill="none"
      stroke="#fff"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M8 42H40"
      stroke="#fff"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// 病案首页登记 & 医保结算清单
export const RecordSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="6"
      y="6"
      width="36"
      height="36"
      rx="3"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <rect
      x="13"
      y="13"
      width="8"
      height="8"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M27 13L35 13"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27 20L35 20"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13 28L35 28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13 35H35"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PermissionsSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="24"
      cy="11"
      r="7"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 41C4 32.1634 12.0589 25 22 25"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M31 42L41 32L37 28L27 38V42H31Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LayoutsSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M38 4H10C8.89543 4 8 4.89543 8 6V42C8 43.1046 8.89543 44 10 44H38C39.1046 44 40 43.1046 40 42V6C40 4.89543 39.1046 4 38 4Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 30L31 30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 36H24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M30 13L22 21L18 17"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DictionarySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M36.686 15.171C37.9364 16.9643 38.8163 19.0352 39.2147 21.2727H44V26.7273H39.2147C38.8163 28.9648 37.9364 31.0357 36.686 32.829L40.0706 36.2137L36.2137 40.0706L32.829 36.686C31.0357 37.9364 28.9648 38.8163 26.7273 39.2147V44H21.2727V39.2147C19.0352 38.8163 16.9643 37.9364 15.171 36.686L11.7863 40.0706L7.92939 36.2137L11.314 32.829C10.0636 31.0357 9.18372 28.9648 8.78533 26.7273H4V21.2727H8.78533C9.18372 19.0352 10.0636 16.9643 11.314 15.171L7.92939 11.7863L11.7863 7.92939L15.171 11.314C16.9643 10.0636 19.0352 9.18372 21.2727 8.78533V4H26.7273V8.78533C28.9648 9.18372 31.0357 10.0636 32.829 11.314L36.2137 7.92939L40.0706 11.7863L36.686 15.171Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M24 29C26.7614 29 29 26.7614 29 24C29 21.2386 26.7614 19 24 19C21.2386 19 19 21.2386 19 24C19 26.7614 21.2386 29 24 29Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
  </svg>
);

export const InsurDictionarySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M44 16C44 22.6274 38.6274 28 32 28C29.9733 28 28.0639 27.4975 26.3896 26.6104L9 44L4 39L21.3896 21.6104C20.5025 19.9361 20 18.0267 20 16C20 9.37258 25.3726 4 32 4C34.0267 4 35.9361 4.50245 37.6104 5.38959L30 13L35 18L42.6104 10.3896C43.4975 12.0639 44 13.9733 44 16Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MedChrigsmSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22 44L21 36"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M42 44V12H26L27 20L28 28L29 36L22 44H42Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M28 28H33"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M27 20H33"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M6 4H25L26 12L27 20L28 28L29 36H21H6V4Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M12 12H19"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M12 20H20"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M12 28H21"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);
export const AdvManagementSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="4"
      y="6"
      width="40"
      height="36"
      rx="3"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M4 14H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M20 24H36"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M20 32H36"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M12 24H14"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M12 32H14"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);
export const TowelSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M36 18H4V26H36V18Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-miterlimit="2"
      stroke-linecap="square"
      stroke-linejoin="bevel"
    />
    <path
      d="M36 12V32C36 34.2091 34.2091 36 32 36H12M12 36H8C5.79086 36 4 34.2091 4 32V8C4 5.79086 5.79086 4 8 4H40C42.2091 4 44 5.79086 44 8V40C44 42.21 42.21 44 40 44H16C13.79 44 12 42.21 12 40V36Z"
      stroke="#757575"
      stroke-width="4"
      stroke-miterlimit="2"
      stroke-linecap="square"
      stroke-linejoin="bevel"
    />
  </svg>
);
export const InHospInfoSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7 35H41C42.1046 35 43 34.1046 43 33V9C43 7.89543 42.1046 7 41 7H7C5.89543 7 5 7.89543 5 9V33C5 34.1046 5.89543 35 7 35Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M14 14V28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M34 14V28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M24 17V25"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M20 21H28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M4 41L44 41"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);
export const AbnormalCaseSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22.799 4.20101L4.41421 22.5858C3.63317 23.3668 3.63316 24.6332 4.41421 25.4142L22.799 43.799C23.58 44.58 24.8464 44.58 25.6274 43.799L44.0122 25.4142C44.7932 24.6332 44.7932 23.3668 44.0122 22.5858L25.6274 4.20101C24.8464 3.41996 23.58 3.41996 22.799 4.20101Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="bevel"
    />
    <path
      d="M18 24H30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M24 18V30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
  </svg>
);
export const RecurringJobSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 20C6 12 10 8 18 8"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M40 30C40 38 36 42 28 42"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M28 18C28 12.4772 32.4772 8 38 8H42V22H28V18Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M6 28H20V32C20 37.5228 15.5228 42 10 42H6V28Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);
export const HqmsDictionarySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M44 14L34 4L30.25 7.75L26.5 11.5L19 19L11.5 26.5L7.75 30.25L4 34L14 44L44 14Z"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M30.25 7.75L7.75 30.25"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 29L13 33"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 24L20 30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19 19L23 23"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24 14L30 20"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M29 9L33 13"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const WtDictionarySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22.8682 24.2982C25.4105 26.7935 26.4138 30.4526 25.4971 33.8863C24.5805 37.32 21.8844 40.0019 18.4325 40.9137C14.9806 41.8256 11.3022 40.8276 8.79375 38.2986C5.02208 34.4141 5.07602 28.2394 8.91499 24.4206C12.754 20.6019 18.9613 20.5482 22.8664 24.3L22.8682 24.2982Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M23 24L40 7"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M30.3052 16.9001L35.7337 22.3001L42.0671 16.0001L36.6385 10.6001L30.3052 16.9001Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
  </svg>
);

export const KpiSettingsSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M24 44C35.0457 44 44 35.0457 44 24C44 12.9543 35.0457 4 24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M24 15C21.2386 15 19 17.2386 19 20C19 21.6358 19.7856 23.0882 21 24.0004L20 32H28L27.0005 24C28.2147 23.0878 29 21.6356 29 20C29 17.2386 26.7614 15 24 15Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const EscalateDictionarySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M34 12V20V21C31.0449 21 28.3892 22.2818 26.5585 24.3198C24.9678 26.0906 24 28.4323 24 31C24 31.5789 24.0492 32.1463 24.1436 32.6983C24.6579 35.7046 26.5143 38.2529 29.0741 39.7046C26.4116 40.5096 22.8776 41 19 41C10.7157 41 4 38.7614 4 36V28V20V12"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M44 31C44 36.5228 39.5228 41 34 41C32.2091 41 30.5281 40.5292 29.0741 39.7046C26.5143 38.2529 24.6579 35.7046 24.1436 32.6983C24.0492 32.1463 24 31.5789 24 31C24 28.4323 24.9678 26.0906 26.5585 24.3198C28.3892 22.2818 31.0449 21 34 21C39.5228 21 44 25.4772 44 31Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M34 12C34 14.7614 27.2843 17 19 17C10.7157 17 4 14.7614 4 12C4 9.23858 10.7157 7 19 7C27.2843 7 34 9.23858 34 12Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 28C4 30.7614 10.7157 33 19 33C20.807 33 22.5393 32.8935 24.1436 32.6983"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 20C4 22.7614 10.7157 25 19 25C21.7563 25 24.339 24.7522 26.5585 24.3198"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M34 27L37.4641 29V33L34 35L30.5359 33V29L34 27Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const InstitutionSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="14"
      cy="29"
      r="5"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx="34"
      cy="29"
      r="5"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx="24"
      cy="9"
      r="5"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24 44C24 38.4772 19.5228 34 14 34C8.47715 34 4 38.4772 4 44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M44 44C44 38.4772 39.5228 34 34 34C28.4772 34 24 38.4772 24 44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M34 24C34 18.4772 29.5228 14 24 14C18.4772 14 14 18.4772 14 24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CalendarSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="4"
      y="8"
      width="40"
      height="36"
      rx="2"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 20H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 32H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 4V12"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M31 4V12"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 20V44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M31 20V44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M44 13V39"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 13L4 39"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 44H34"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ManagementSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M32 6H22V42H32V6Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M42 6H32V42H42V6Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M10 6L18 7L14.5 42L6 41L10 6Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M37 18V15"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27 18V15"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SearchSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M38 4H10C8.89543 4 8 4.89543 8 6V42C8 43.1046 8.89543 44 10 44H38C39.1046 44 40 43.1046 40 42V6C40 4.89543 39.1046 4 38 4Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M28 16C28 17.3807 27.4404 18.6307 26.5355 19.5355C25.6307 20.4404 24.3807 21 23 21C20.2386 21 18 18.7614 18 16C18 13.2386 20.2386 11 23 11C25.7614 11 28 13.2386 28 16Z"
      fill="none"
    />
    <path
      d="M30 23L26.5355 19.5355M26.5355 19.5355C27.4404 18.6307 28 17.3807 28 16C28 13.2386 25.7614 11 23 11C20.2386 11 18 13.2386 18 16C18 18.7614 20.2386 21 23 21C24.3807 21 25.6307 20.4404 26.5355 19.5355Z"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 30L31 30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 36H24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const printSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M38 20V8C38 6.89543 37.1046 6 36 6H12C10.8954 6 10 6.89543 10 8V20"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <rect
      x="6"
      y="20"
      width="36"
      height="22"
      rx="2"
      stroke="#757575"
      strokeWidth="4"
    />
    <path
      d="M20 34H35V42H20V34Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M12 26H15"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);

export const outgoingSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 35C18 32.7909 16.2091 31 14 31C11.7909 31 10 32.7909 10 35C10 37.2091 11.7909 39 14 39C16.2091 39 18 37.2091 18 35Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linejoin="round"
    />
    <path
      d="M37 35C37 32.7909 35.2091 31 33 31C30.7909 31 29 32.7909 29 35C29 37.2091 30.7909 39 33 39C35.2091 39 37 37.2091 37 35Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linejoin="round"
    />
    <path
      d="M4 35H10"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
    />
    <path
      d="M18 35H29"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
    />
    <path
      d="M37 35H44"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
    />
    <path
      d="M38 19L44 13L38 7"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M4 13H44"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const simpleQuerySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24 8L4 40H44L24 8Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linejoin="round"
    />
    <path
      d="M30 32L24 28L18 32"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M24 28V22"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const StatisticSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M44 5H3.99998V17H44V5Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M3.99998 41.0301L16.1756 28.7293L22.7549 35.0301L30.7982 27L35.2786 31.368"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M44 16.1719V42.1719"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M3.99998 16.1719V30.1719"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M13.0155 43H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M17 11H38"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M9.99998 10.9966H11"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
  </svg>
);

export const MonitorSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14 25C14 19.4772 18.4772 15 24 15C29.5228 15 34 19.4772 34 25V41H14V25Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M24 5V8"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M35.8918 9.32823L33.9634 11.6264"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M42.2187 20.2873L39.2642 20.8083"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.78116 20.2874L8.73558 20.8083"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.1086 9.32802L14.037 11.6262"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 41H43"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DetailSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13 38H41V16H30V4H13V38Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M30 4L41 16"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 20V44H28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19 20H23"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M19 28H31"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
  </svg>
);

export const Detail2Svg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M39 4H11C9.89543 4 9 4.89543 9 6V42C9 43.1046 9.89543 44 11 44H39C40.1046 44 41 43.1046 41 42V6C41 4.89543 40.1046 4 39 4Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 30L31 30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 36H24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="17"
      y="12"
      width="14"
      height="10"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const RuleSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M41.5 10H35.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27.5 6V14"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27.5 10L5.5 10"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.5 24H5.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21.5 20V28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M43.5 24H21.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M41.5 38H35.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27.5 34V42"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27.5 38H5.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CustomRuleSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16 15C16 13.3431 17.3431 12 19 12H37C38.6569 12 40 13.3431 40 15V33C40 34.6569 38.6569 36 37 36H19C17.3431 36 16 34.6569 16 33V15Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linejoin="round"
    />
    <path
      d="M8 4L8 44"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M8 19L16 19"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M8 29L16 29"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M22 24L34 24"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M28 18V30"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const BarSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path fillRule="evenodd" clipRule="evenodd" d="M4 42H44H4Z" fill="none" />
    <path
      d="M4 42H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="8"
      y="28"
      width="6"
      height="14"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <rect
      x="21"
      y="18"
      width="6"
      height="24"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <rect
      x="34"
      y="6"
      width="6"
      height="36"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
  </svg>
);

export const Bar2Svg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4 42H8V24H15V42H19.9406V6H27.9792V33.0659H34.0203V16.125H40.0687V42H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PieSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M44 24C44 35.0457 35.0457 44 24 44C12.9543 44 4 35.0457 4 24C4 12.9543 12.9543 4 24 4V24H44Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M43.0844 18H30V4.91553C36.2202 6.86917 41.1308 11.7798 43.0844 18Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LineSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4 4V44H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 38C10 38 15.3125 4 27 4C38.6875 4 44 38 44 38"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 24L44 24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
      stroke-dasharray="2 6"
    />
  </svg>
);

export const ScatterSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="34"
      cy="14"
      r="9"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx="12"
      cy="25"
      r="7"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx="29"
      cy="37"
      r="5"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MonitorCameraSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 10V24C8 32.8366 15.1634 40 24 40V40C32.8366 40 40 32.8366 40 24V10"
      stroke="#757575"
      strokeWidth="4"
    />
    <path
      d="M4 10H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M24 30C27.3137 30 30 27.3137 30 24C30 20.6863 27.3137 18 24 18C20.6863 18 18 20.6863 18 24C18 27.3137 20.6863 30 24 30Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="bevel"
    />
  </svg>
);

export const ToolSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M44 16C44 22.6274 38.6274 28 32 28C29.9733 28 28.0639 27.4975 26.3896 26.6104L9 44L4 39L21.3896 21.6104C20.5025 19.9361 20 18.0267 20 16C20 9.37258 25.3726 4 32 4C34.0267 4 35.9361 4.50245 37.6104 5.38959L30 13L35 18L42.6104 10.3896C43.4975 12.0639 44 13.9733 44 16Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="square"
      stroke-linejoin="bevel"
    />
  </svg>
);

export const MonitorImportantSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24 34C32.2843 34 39 27.2843 39 19C39 10.7157 32.2843 4 24 4C15.7157 4 9 10.7157 9 19C9 27.2843 15.7157 34 24 34Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="bevel"
    />
    <path
      d="M24 25C27.3137 25 30 22.3137 30 19C30 15.6863 27.3137 13 24 13C20.6863 13 18 15.6863 18 19C18 22.3137 20.6863 25 24 25Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="bevel"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.3686 34L16 44H32L28.6037 34H19.3686Z"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M12 44H36"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);

export const ExternalSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 31L8.00002 42C8.00002 43.1046 8.89545 44 10 44H38C39.1046 44 40 43.1046 40 42V31"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M38 14H10C8.89543 14 8 14.8954 8 16L8.00002 22C8.00002 23.1046 8.89545 24 10 24H38C39.1046 24 40 23.1046 40 22V16C40 14.8954 39.1046 14 38 14Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="bevel"
    />
    <path
      d="M16 4V8"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M24 4V8"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M32 4V8"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M16 34H32"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);
export const EnquireSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M37 16C34.2386 16 32 13.7614 32 11C32 8.23858 34.2386 6 37 6C39.7614 6 42 8.23858 42 11C42 13.7614 39.7614 16 37 16Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-miterlimit="2"
    />
    <path
      d="M12 12C9.79086 12 8 10.2091 8 8C8 5.79086 9.79086 4 12 4C14.2091 4 16 5.79086 16 8C16 10.2091 14.2091 12 12 12Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-miterlimit="2"
    />
    <path
      d="M26 39L32 34V28C32 24.5339 34 22 37 22C40 22 42 24.5339 42 28V32.8372V42"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M24 33L18 28V24C18 20.5339 16 18 13 18C10 18 8 20.5339 8 24V26.8372V42"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const ReportSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7 37C7 29.2967 7 11 7 11C7 7.68629 9.68629 5 13 5H35V31C35 31 18.2326 31 13 31C9.7 31 7 33.6842 7 37Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M35 31C35 31 14.1537 31 13 31C9.68629 31 7 33.6863 7 37C7 40.3137 9.68629 43 13 43C15.2091 43 25.8758 43 41 43V7"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 37H34"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CustomDiseaseSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 44C13.3137 44 16 41.3137 16 38V23.5147V4H4V38C4 41.3137 6.68629 44 10 44Z"
      fill="none"
    />
    <path
      d="M10 44C13.3137 44 16 41.3137 16 38V23.5147M10 44C6.68629 44 4 41.3137 4 38V4H16V23.5147M10 44H44V32H24.4853M5.75736 42.2426C8.10051 44.5858 11.8995 44.5858 14.2426 42.2426L24.4853 32M16 23.5147L35.0147 4.5L35.4853 4L43.9853 12.5L24.4853 32"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M14.2427 42.2426L43.9853 12.5L35.4853 4L16 23.5147"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M24.4853 32H44V44H12.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M24.4853 32H44V44H12.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M10 44C13.3137 44 16 41.3137 16 38V23.5147V4H4V38C4 41.3137 6.68629 44 10 44Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);

export const Report2Svg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 7C5 5.34315 6.34315 4 8 4H32C33.6569 4 35 5.34315 35 7V44H8C6.34315 44 5 42.6569 5 41V7Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M35 24C35 22.8954 35.8954 22 37 22H41C42.1046 22 43 22.8954 43 24V41C43 42.6569 41.6569 44 40 44H35V24Z"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M11 12H19"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11 19H23"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const OperSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <ellipse
      cx="24"
      cy="39"
      rx="18"
      ry="6"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path d="M6 39H42" stroke="#757575" strokeWidth="4" strokeLinecap="round" />
    <path
      d="M6 39L23.9999 4L42 39"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const QualitySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M37 16C39.7614 16 42 13.7614 42 11C42 8.23858 39.7614 6 37 6C34.2386 6 32 8.23858 32 11C32 13.7614 34.2386 16 37 16Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M11 42C13.7614 42 16 39.7614 16 37C16 34.2386 13.7614 32 11 32C8.23858 32 6 34.2386 6 37C6 39.7614 8.23858 42 11 42Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M37 16V35.5042C37 39.0917 34.0917 42 30.5042 42V42C26.9166 42 24.0083 39.0917 24.0083 35.5042V12.5042C24.0083 8.91201 21.0963 6 17.5042 6V6C13.912 6 11 8.91201 11 12.5042L11 32"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SdSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M42 16V9C42 7.34315 40.6569 6 39 6H9C7.34315 6 6 7.34315 6 9V16"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 32V39C6 40.6569 7.34315 42 9 42H39C40.6569 42 42 40.6569 42 39V32"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 24H13.075L20 16L27 32L33.975 24H43"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ServiceabilitySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M31.8785 37C29.0529 39.0738 26.2237 40.5872 24 41.3262C17 39 4 29 4 18C4 11.9249 8.92487 7 15 7C18.7203 7 22.0093 8.8469 24 11.6738C25.9907 8.8469 29.2797 7 33 7C39.0751 7 44 11.9249 44 18C44 19.7465 43.6723 21.4677 43.0929 23.1371"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27 29H31L34 25L37 33L39.9625 29H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ComplicationSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 35H6C4.89543 35 4 34.1046 4 33V11C4 9.89543 4.89543 9 6 9H31C32.1046 9 33 9.89543 33 11V17.8987C33 19.1602 33.7892 20.287 34.9748 20.7181L42.0252 23.2819C43.2108 23.713 44 24.8398 44 26.1013V33C44 34.1046 43.1046 35 42 35H38"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M18 35H30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M12 19L20 19"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16 15V23"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx="14"
      cy="35"
      r="4"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
    />
    <circle
      cx="34"
      cy="35"
      r="4"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
    />
  </svg>
);

export const CombineQuerySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M23 20L23 6L6 6L6 20L23 20Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M42 42V28L25 28L25 42H42Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M31 6V20H42V6H31Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M6 28L6 42H17V28H6Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
  </svg>
);

export const SimpleQuerySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M33 41C27.5203 44.0026 23 44 17 42C10.9236 39.9745 7 33 7 28C7 25.2562 11.1135 23.6282 12.5286 23.1494C12.8074 23.055 13 22.7966 13 22.5023V15C13 13.067 14.567 11.5 16.5 11.5C18.433 11.5 20 13.067 20 15V12.5C20 10.567 21.567 9 23.5 9C25.433 9 27 10.567 27 12.5V15C27 13.067 28.567 11.5 30.5 11.5C32.433 11.5 34 13.067 34 15V7.49999C34 5.567 35.567 4 37.5 4C39.433 4 41 5.567 41 7.49999V28.2319C41 30.7041 40.4077 33.1603 38.962 35.1657C37.4919 37.2049 35.3574 39.7083 33 41Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const InpatientSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 17V39"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M42 25L42 39"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M26 15H38"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11 22H17"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 28L42 28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 34L42 34"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M32 9V21"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const OutpatientSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 23.9372V10C18 6.68629 20.6863 4 24 4C27.3137 4 30 6.68629 30 10V12.0057"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M30 24.0034V37.9999C30 41.3136 27.3137 43.9999 24 43.9999C20.6863 43.9999 18 41.3136 18 37.9999V35.9699"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M24 30H9.98415C6.67919 30 4 27.3137 4 24C4 20.6863 6.67919 18 9.98415 18H11.9886"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M24 18H37.9888C41.3087 18 44 20.6863 44 24C44 27.3137 41.3087 30 37.9888 30H36.0663"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
  </svg>
);

export const OutpatientDoctorSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M38.1684 22.262L19.0766 41.3539L6.34863 28.626L25.4405 9.53409"
      fill="none"
    />
    <path
      d="M38.1684 22.262L19.0766 41.3539L6.34863 28.626L25.4405 9.53409"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="bevel"
    />
    <path
      d="M21.9053 5.99854L41.7043 25.7975"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M14.834 28.626L19.0766 32.8686"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M6.34961 41.353L12.7128 34.9898"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
    <path
      d="M31.8047 15.8979L35.3394 12.3632"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
    />
  </svg>
);

export const ObspatientSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M26 44C30.3462 40.9919 32.6627 37.9513 32.9493 34.8782C33.2359 31.805 32.308 29.5123 30.1657 28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M27.6553 28.2227C30.1406 28.2227 32.1553 26.2079 32.1553 23.7227C32.1553 21.2374 30.1406 19.2227 27.6553 19.2227C25.17 19.2227 23.1553 21.2374 23.1553 23.7227C23.1553 26.2079 25.17 28.2227 27.6553 28.2227Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
    />
    <path
      d="M24.2882 27L18.7783 32.5772L9.58594 23.3848L27.9707 4.99999L37.1631 14.1924L30.9764 20.3791"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.55762 28.1357L14.4195 35.8141"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path d="M6 44H42" stroke="#757575" strokeWidth="4" strokeLinecap="round" />
  </svg>
);

export const ODMManagementSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="6"
      y="28"
      width="36"
      height="14"
      rx="4"
      stroke="#757575"
      stroke-width="4"
    />
    <path
      d="M20 7H10C7.79086 7 6 8.79086 6 11V17C6 19.2091 7.79086 21 10 21H20"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="square"
    />
    <circle
      cx="34"
      cy="14"
      r="8"
      fill="none"
      stroke="#757575"
      stroke-width="4"
    />
    <circle cx="34" cy="14" r="3" fill="#757575" />
  </svg>
);

export const ODMOutManagementSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M32.9037 13.9272C31.2464 17.1588 27.8814 19.3702 24 19.3702C20.1185 19.3702 16.7536 17.1588 15.0963 13.9272C11.3982 16.6591 9 21.0495 9 26.0001C9 26.8178 9.06543 27.6202 9.19135 28.4024C9.45807 28.3811 9.72775 28.3702 9.99996 28.3702C15.5228 28.3702 20 32.8474 20 38.3702C20 39.0665 19.9288 39.7461 19.7934 40.4022C21.128 40.7914 22.5397 41.0001 24 41.0001C25.4603 41.0001 26.8719 40.7914 28.2066 40.4022C28.0711 39.7461 28 39.0665 28 38.3702C28 32.8474 32.4771 28.3702 38 28.3702C38.2722 28.3702 38.5419 28.3811 38.8087 28.4024C38.9346 27.6202 39 26.8178 39 26.0001C39 21.0495 36.6017 16.6591 32.9037 13.9272Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M24 13C26.2091 13 28 11.2091 28 9C28 6.79086 26.2091 5 24 5C21.7909 5 20 6.79086 20 9C20 11.2091 21.7909 13 24 13Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9 43C11.2091 43 13 41.2091 13 39C13 36.7909 11.2091 35 9 35C6.79086 35 5 36.7909 5 39C5 41.2091 6.79086 43 9 43Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M39 43C41.2091 43 43 41.2091 43 39C43 36.7909 41.2091 35 39 35C36.7909 35 35 36.7909 35 39C35 41.2091 36.7909 43 39 43Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const WorkLoadItemSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21.9474 4V10M10.0526 7H4C4 7 4 16 4 21C4 26 8 32 16 32C24 32 28 26 28 21C28 16 28 7 28 7H21.9474H10.0526ZM10.0526 4V10V4Z"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M40 23C42.2091 23 44 21.2091 44 19C44 16.7909 42.2091 15 40 15C37.7909 15 36 16.7909 36 19C36 21.2091 37.7909 23 40 23Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16 32C16 38.6274 21.3726 44 28 44C34.6274 44 40 38.6274 40 32V23"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const WorkLoadItem2Svg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M34 10H14C12.8954 10 12 10.8954 12 12L12 42C12 43.1046 12.8954 44 14 44H34C35.1046 44 36 43.1046 36 42V12C36 10.8954 35.1046 10 34 10Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M12 18H36"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M12 15V21"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M36 15V21"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M32 4H16L16 10H32V4Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M20 31H28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M24 27V35"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
  </svg>
);

export const WorkLoadItem3Svg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.61497 22.5852C4.4434 21.4136 4.4434 19.5141 5.61497 18.3425L18.3429 5.61463C19.5145 4.44305 21.414 4.44305 22.5855 5.61462L42.3845 25.4136C43.5561 26.5852 43.5561 28.4847 42.3845 29.6563L29.6566 42.3842C28.485 43.5557 26.5855 43.5557 25.414 42.3842L5.61497 22.5852Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <circle
      cx="14.8079"
      cy="20.4648"
      r="2"
      transform="rotate(-45 14.8079 20.4648)"
      fill="#757575"
    />
    <circle
      cx="23.2928"
      cy="28.9492"
      r="2"
      transform="rotate(-45 23.2928 28.9492)"
      fill="#757575"
    />
    <circle
      cx="19.0501"
      cy="24.707"
      r="2"
      transform="rotate(-45 19.0501 24.707)"
      fill="#757575"
    />
    <circle
      cx="27.5364"
      cy="33.1934"
      r="2"
      transform="rotate(-45 27.5364 33.1934)"
      fill="#757575"
    />
    <circle
      cx="20.4642"
      cy="14.8066"
      r="2"
      transform="rotate(-45 20.4642 14.8066)"
      fill="#757575"
    />
    <circle
      cx="28.95"
      cy="23.293"
      r="2"
      transform="rotate(-45 28.95 23.293)"
      fill="#757575"
    />
    <circle
      cx="24.7073"
      cy="19.0508"
      r="2"
      transform="rotate(-45 24.7073 19.0508)"
      fill="#757575"
    />
    <circle
      cx="33.1927"
      cy="27.5352"
      r="2"
      transform="rotate(-45 33.1927 27.5352)"
      fill="#757575"
    />
  </svg>
);

export const HierarchyBedSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="5"
      y="16"
      width="38"
      height="26"
      rx="3"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M19 8H29V4H19V8ZM30 9V16H34V9H30ZM18 16V9H14V16H18ZM29 8C29.5523 8 30 8.44772 30 9H34C34 6.23858 31.7614 4 29 4V8ZM19 4C16.2386 4 14 6.23858 14 9H18C18 8.44772 18.4477 8 19 8V4Z"
      fill="#757575"
    />
    <path
      d="M18 29L30 29"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24 23V35"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmployeeSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="14"
      cy="29"
      r="5"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx="34"
      cy="29"
      r="5"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx="24"
      cy="9"
      r="5"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24 44C24 38.4772 19.5228 34 14 34C8.47715 34 4 38.4772 4 44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M44 44C44 38.4772 39.5228 34 34 34C28.4772 34 24 38.4772 24 44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M34 24C34 18.4772 29.5228 14 24 14C18.4772 14 14 18.4772 14 24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const RecordListSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="4"
      y="6"
      width="40"
      height="36"
      rx="3"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 14H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20 24H36"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20 32H36"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 24H14"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 32H14"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const AnalysisSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M44 5H3.99998V17H44V5Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M3.99998 41.0301L16.1756 28.7293L22.7549 35.0301L30.7982 27L35.2786 31.368"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M44 16.1719V42.1719"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M3.99998 16.1719V30.1719"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M13.0155 43H44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M17 11H38"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M9.99998 10.9966H11"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
  </svg>
);

export const WardSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M44 24V9H24H4V24V39H24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 9L24 24L44 9"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="31"
      y="33"
      width="12"
      height="8"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M40 33V30C40 28.3431 38.6569 27 37 27C35.3431 27 34 28.3431 34 30V33"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SigninSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M44 24V9H24H4V24V39H24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M31 36L36 40L44 30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4 9L24 24L44 9"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SealSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M42 4H6V14H42V4Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M42 19H6V29H42V19Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M42 34H6V44H42V34Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path d="M21 9H27" stroke="#757575" strokeWidth="4" strokeLinecap="round" />
    <path
      d="M21 24H27"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M21 39H27"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
  </svg>
);

export const UploadSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24.0079 41L23.9995 23"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M40.5178 34.3161C43.8044 32.005 45.2136 27.8302 44.0001 24C42.7866 20.1698 39.0705 18.0714 35.0527 18.0745H32.7317C31.2144 12.1613 26.2082 7.79572 20.1435 7.0972C14.0787 6.39868 8.21121 9.5118 5.38931 14.9253C2.56741 20.3388 3.37545 26.9317 7.42115 31.5035"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M30.3638 27.6359L23.9998 21.272L17.6358 27.6359"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const DataManagementSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 18V9C6 7.34315 7.34315 6 9 6H39C40.6569 6 42 7.34315 42 9V18"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M32 24V31"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M24 15V31"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M16 19V31"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M6 30V39C6 40.6569 7.34315 42 9 42H39C40.6569 42 42 40.6569 42 39V30"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const CmiSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24 14C26.7614 14 29 11.7614 29 9C29 6.23858 26.7614 4 24 4C21.2386 4 19 6.23858 19 9C19 11.7614 21.2386 14 24 14Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24 44C26.7614 44 29 41.7614 29 39C29 36.2386 26.7614 34 24 34C21.2386 34 19 36.2386 19 39C19 41.7614 21.2386 44 24 44Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 19H4V29H14V19Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M44 19H34V29H44V19Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19 9H9V19"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19 39H9V29"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M29 9H40V19"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M29 39H39V29"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ImportSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 30L10 6H38L43 30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 30H14.9091L16.7273 36H31.2727L33.0909 30H43V43H5V30Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M18 20L24 26L30 20"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24 26V14"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const TransferSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M37 28.3923V35.4066C37 39.048 34.0885 42 30.497 42C26.9054 42 23.9939 39.048 23.9939 35.4066L24.0061 13.1429C24.0061 9.19797 21.0946 6 17.503 6C13.9115 6 11 9.19797 11 13.1429V28.3923"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M43 31L37 25L31 31"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M43 10.7273C43 15.1818 37 19 37 19C37 19 31 15.1818 31 10.7273C31 9.20831 31.6321 7.75155 32.7574 6.67748C33.8826 5.60341 35.4087 5 37 5C38.5913 5 40.1174 5.60341 41.2426 6.67748C42.3679 7.75155 43 9.20831 43 10.7273Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 34.7273C17 39.1818 11 43 11 43C11 43 5 39.1818 5 34.7273C5 33.2083 5.63214 31.7516 6.75736 30.6775C7.88258 29.6034 9.4087 29 11 29C12.5913 29 14.1174 29.6034 15.2426 30.6775C16.3679 31.7516 17 33.2083 17 34.7273Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle cx="37" cy="11" r="2" fill="#757575" />
    <circle cx="11" cy="35" r="2" fill="#757575" />
  </svg>
);

export const SummarySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M40 9L37 6H8L26 24L8 42H37L40 39"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const GlobalConfigurationSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M36.686 15.171C37.9364 16.9643 38.8163 19.0352 39.2147 21.2727H44V26.7273H39.2147C38.8163 28.9648 37.9364 31.0357 36.686 32.829L40.0706 36.2137L36.2137 40.0706L32.829 36.686C31.0357 37.9364 28.9648 38.8163 26.7273 39.2147V44H21.2727V39.2147C19.0352 38.8163 16.9643 37.9364 15.171 36.686L11.7863 40.0706L7.92939 36.2137L11.314 32.829C10.0636 31.0357 9.18372 28.9648 8.78533 26.7273H4V21.2727H8.78533C9.18372 19.0352 10.0636 16.9643 11.314 15.171L7.92939 11.7863L11.7863 7.92939L15.171 11.314C16.9643 10.0636 19.0352 9.18372 21.2727 8.78533V4H26.7273V8.78533C28.9648 9.18372 31.0357 10.0636 32.829 11.314L36.2137 7.92939L40.0706 11.7863L36.686 15.171Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M24 29C26.7614 29 29 26.7614 29 24C29 21.2386 26.7614 19 24 19C21.2386 19 19 21.2386 19 24C19 26.7614 21.2386 29 24 29Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
  </svg>
);

export const TransactionSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M39 6H9C7.34315 6 6 7.34315 6 9V39C6 40.6569 7.34315 42 9 42H39C40.6569 42 42 40.6569 42 39V9C42 7.34315 40.6569 6 39 6Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21 31L26 35L34 25"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 15H34"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 23L22 23"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const FinancingSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.0002 14.3848C19.1256 16.0002 24.0085 16.0002 24.0085 16.0002C24.0085 16.0002 28.8802 16.0002 33.0002 14.3848C37.502 19.6386 40.6566 26.5646 42.7299 32.3977C44.8289 38.3029 40.2008 44.0002 33.9336 44.0002H14.0199C7.76837 44.0002 3.14607 38.329 5.23448 32.4366C7.29812 26.614 10.455 19.6856 15.0002 14.3848Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M18 28H30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18 34H30"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24.0088 28V38"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M30 22L24 28L18 22"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M24 16C31.1797 16 37 13.3137 37 10C37 6.68629 31.1797 4 24 4C16.8203 4 11 6.68629 11 10C11 13.3137 16.8203 16 24 16Z"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SystemInfoSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24 44C29.5228 44 34.5228 41.7614 38.1421 38.1421C41.7614 34.5228 44 29.5228 44 24C44 18.4772 41.7614 13.4772 38.1421 9.85786C34.5228 6.23858 29.5228 4 24 4C18.4772 4 13.4772 6.23858 9.85786 9.85786C6.23858 13.4772 4 18.4772 4 24C4 29.5228 6.23858 34.5228 9.85786 38.1421C13.4772 41.7614 18.4772 44 24 44Z"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M24 11C25.3807 11 26.5 12.1193 26.5 13.5C26.5 14.8807 25.3807 16 24 16C22.6193 16 21.5 14.8807 21.5 13.5C21.5 12.1193 22.6193 11 24 11Z"
      fill="#757575"
    />
    <path
      d="M24.5 34V20H23.5H22.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21 34H28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ExcelSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 15V6C8 4.89543 8.89543 4 10 4H38C39.1046 4 40 4.89543 40 6V42C40 43.1046 39.1046 44 38 44H10C8.89543 44 8 43.1046 8 42V33"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M31 15H34"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M28 23H34"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <path
      d="M28 31H34"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
    />
    <rect
      x="4"
      y="15"
      width="18"
      height="18"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 21L16 27"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16 21L10 27"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const HisExcelSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 4H30L40 14V42C40 43.1046 39.1046 44 38 44H10C8.89543 44 8 43.1046 8 42V6C8 4.89543 8.89543 4 10 4Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linejoin="round"
    />
    <path
      d="M29 18H19V34H29"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M29 26H19"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const ImportAndExportSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14 25.9999L5 34.9999L14 43.9999"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 35.0083H22.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M34.0005 18L43.0005 27L34.0005 36"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M43 27.0084H25.5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.5 24V7.5L43.5 7.5V15"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const FundSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <ellipse
      cx="14"
      cy="10"
      rx="10"
      ry="5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M4 10C4 10 4 14.2386 4 17C4 19.7614 8.47715 22 14 22C19.5228 22 24 19.7614 24 17C24 15.3644 24 10 24 10"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M4 17C4 17 4 21.2386 4 24C4 26.7614 8.47715 29 14 29C19.5228 29 24 26.7614 24 24C24 22.3644 24 17 24 17"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M4 24C4 24 4 28.2386 4 31C4 33.7614 8.47715 36 14 36C19.5228 36 24 33.7614 24 31C24 29.3644 24 24 24 24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M4 31C4 31 4 35.2386 4 38C4 40.7614 8.47715 43 14 43C19.5228 43 24 40.7614 24 38C24 36.3644 24 31 24 31"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <ellipse
      cx="34"
      cy="24"
      rx="10"
      ry="5"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M24 24C24 24 24 28.2386 24 31C24 33.7614 28.4772 36 34 36C39.5228 36 44 33.7614 44 31C44 29.3644 44 24 44 24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
    <path
      d="M24 31C24 31 24 35.2386 24 38C24 40.7614 28.4772 43 34 43C39.5228 43 44 40.7614 44 38C44 36.3644 44 31 44 31"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="square"
      strokeLinejoin="bevel"
    />
  </svg>
);

export const ContrastSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="7"
      y="7"
      width="17"
      height="34"
      fill="none"
      stroke="#757575"
      strokeWidth="4"
      strokeLinejoin="round"
    />
    <path
      d="M24 7H28"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M33 7H35"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M33 41H35"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M41 7V9"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M41 15V17"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M41 23V25"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M41 31V33"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M41 39V41"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27 41H24"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24 4V44"
      stroke="#757575"
      strokeWidth="4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const TableSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M42 6H6C4.89543 6 4 6.89543 4 8V40C4 41.1046 4.89543 42 6 42H42C43.1046 42 44 41.1046 44 40V8C44 6.89543 43.1046 6 42 6Z"
      fill="none"
      stroke="#757575"
      stroke-width="4"
      stroke-linejoin="round"
    />
    <path
      d="M4 18H44"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M17.5 18V42"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M30.5 18V42"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M4 30H44"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M44 8V40C44 41.1046 43.1046 42 42 42H6C4.89543 42 4 41.1046 4 40V8"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const SyncSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M30 7.979L19 8.00006V18.0001H4V32H20.9995"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M20 42L31 41.9789V31.5789H44V18H30.3448"
      stroke="#757575"
      stroke-width="4"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
