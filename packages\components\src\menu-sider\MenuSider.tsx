import Sider from 'antd/es/layout/Sider';
import React, { Component, useEffect, useLayoutEffect, useState } from 'react';
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';
import './index.less';
import { Avatar, Tooltip, Menu } from 'antd';
import { Link, Location, useLocation, history } from 'umi';
import { ReactComponent as IconHome } from '../assets/icon_home.svg';
import { getAllMenusContainsHidden, getAllMenusOnlySubSystems } from './utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  findCurrentPathParents,
  flattenMenuData,
  flattenMenuOrder,
  generateMenuWithOrder,
} from '@uni/utils/src/menu-utils';
import {
  getHeaderIds,
  headerMenuKeysToHeaderIds,
  isEmptyValues,
} from '@uni/utils/src/utils';
import uniq from 'lodash/uniq';
import { headerMenu } from '../../../../src/layouts/menuData';
import {
  getDevicePixelRatio,
  isDevicePixelRatioMax,
  matchMediaQueryString,
} from '@uni/utils/src/ratio';

// import logo from '@/assets/logo.png';

const { SubMenu } = Menu;
const qs = require('qs');

export interface MenuData {
  route: string;
  query?: object;
  name: string;
  headerMenuKey?: string;
  headerMenuItemActualRoute?: string;
  // 图标
  icon?: any;
  iconSelected?: any;
  isSubSystem?: boolean;
  hideInConfiguration?: boolean;
  // 用于记录当前这个路由的上级路径
  parentRoutes?: string[];
  children?: MenuData[];

  order?: number;
  customTitle?: string;
}

export interface HeaderMenuItem {
  id: string;
  key: string[];
  name: string;
  // 图标
  icon?: any;
  // 背景图片
  bg?: any;
  // 是否展示于主界面
  mainShow?: boolean;

  // 顺序
  order?: number;
  childrenOrder?: any[];
}

interface MenuSiderProps {
  headerMenu?: any[];

  bizRoutes?: any[];

  menuData: MenuData[];

  access: any;

  menuOrder: any[];
}

const configTitle =
  (window as any).externalConfig?.['common']?.title ?? '智慧病案一体化平台';

const leftNewTab =
  (window as any).externalConfig?.['common']?.leftNewTab ?? false;

const multipleSlashRootPath = [
  '/qualityControl/doctor',
  '/chs/analysis',
  '/chs/dip',
];

// 根据前缀判断这个前缀的是不是需要多次
// 匹配的前缀，slash次数
const pathNeedMultiSlash = {
  '/chs': 2,
  '/qualityControl': 2,
};

const MenuSider = (props: MenuSiderProps) => {
  const [collapsed, setCollapsed] = useState(false);

  const [selectedHeaderMenuKey, setSelectedHeaderMenuKey] = useState('');

  const [selectedKeys, setSelectedKeys] = useState([]);
  const [openedKeys, setOpenedKeys] = useState([]);

  const [devicePixelRatio, setDevicePixelRatio] = useState(
    getDevicePixelRatio(),
  );

  const [menuOrder, setMenuOrder] = useState<{
    [key: string]: {
      index?: number;
      parentRoute?: string;
      customTitle?: string;
    };
  }>({});

  const location: Location = useLocation();

  useEffect(() => {
    Emitter.on('HEADER_MENU_SELECT', (data) => {
      setSelectedHeaderMenuKey(data?.route);
      if (data?.headerMenuItemActualRoute) {
        history?.push(data?.headerMenuItemActualRoute);
      }
    });

    Emitter.on('HEADER_MENU_LOADED', () => {
      getCurrentSelectSubSystemByLocation();
    });

    (global?.window as any)?.eventEmitter?.on(
      'SIDER_MENU_ORDER_CHANGE',
      (menuOrder) => {
        setMenuOrder(flattenMenuOrder(menuOrder));
      },
    );

    history.listen((location, action) => {
      if (action === 'POP') {
        getCurrentSelectSubSystemByLocationParam(location);
      }

      if (action === 'PUSH') {
        getCurrentSelectSubSystemByLocationParam(location);
      }
    });
    matchMedia(matchMediaQueryString).addEventListener('change', () => {
      setDevicePixelRatio(getDevicePixelRatio());
    });

    matchMedia(matchMediaQueryString).addEventListener('change', () => {
      setDevicePixelRatio(getDevicePixelRatio());
    });

    return () => {
      Emitter.off('HEADER_MENU_SELECT');
      Emitter.off('HEADER_MENU_LOADED');
      (global?.window as any)?.eventEmitter?.off('SIDER_MENU_ORDER_CHANGE');

      matchMedia(matchMediaQueryString).removeEventListener('change', () => {});
    };
  }, []);

  useEffect(() => {
    setMenuOrder(flattenMenuOrder(props?.menuOrder));
  }, [props?.menuOrder]);

  useEffect(() => {
    getCurrentOpenKeysByLocation();
    getCurrentSelectedKeysByLocation();
  }, [location?.pathname, props?.menuData]);

  useEffect(() => {
    if (!collapsed) {
      getCurrentOpenKeysByLocation();
    }
  }, [collapsed, props?.menuData]);

  // 用于在其他地方暴露menuData
  useEffect(() => {
    (global?.window as any)?.eventEmitter.on('MENU_DATA_REQUEST', () => {
      (global?.window as any)?.eventEmitter.emit(
        'MENU_DATA_RESPONSE',
        // props?.menuData,
        props?.menuData,
      );
    });

    return () => {
      (global?.window as any)?.eventEmitter.off('MENU_DATA_REQUEST');
    };
  }, [props?.menuData]);
  // }, [menuData]);

  const getCurrentOpenKeysByLocation = () => {
    let locationOpenedKeys: string[] = [];
    if (location?.pathname) {
      let currentMenuItem = findCurrentPathParents(
        location?.pathname,
        props?.menuData,
      );

      let subSystemMenus = getAllMenusOnlySubSystems(props?.menuData);
      if (currentMenuItem) {
        currentMenuItem?.parentRoutes?.forEach((route) => {
          if (
            subSystemMenus?.find((item) => item?.route === route) === undefined
          ) {
            locationOpenedKeys.push(route);
          }
        });
      }
    }

    if (!collapsed) {
      setOpenedKeys([...new Set([...locationOpenedKeys, ...openedKeys])]);
    }

    return locationOpenedKeys;
  };

  // TODO 通过location 反查 route
  const getCurrentSelectSubSystemByLocation = () => {
    if (location?.pathname) {
      let paths = location?.pathname?.split('/').filter((item) => item);
      if (paths?.length > 0) {
        let rootPath = `/${paths?.at(0)}`;
        // 做一个多层的单独判断
        if (
          Object.keys(pathNeedMultiSlash)?.findIndex(
            (item) => item === rootPath,
          ) > -1
        ) {
          let slashCount = pathNeedMultiSlash?.[rootPath];
          rootPath = '';
          for (let i = 0; i < slashCount; i++) {
            rootPath += `/${paths?.at(i)}`;
          }
        }

        // dmr 多一套判定
        if (rootPath === '/dmr') {
          if (location?.pathname?.startsWith('/dmr/examine')) {
            rootPath = '/dmr/examine';
          }
        }

        Emitter.emit('HEADER_MENU_SELECT_BY_LOCATION', {
          rootPath: rootPath,
        });
        // chs 单独判定
        // if (
        //   multipleSlashRootPath?.find(
        //     (item) => location?.pathname?.indexOf(item) > -1,
        //   )
        // ) {
        //   rootPath = multipleSlashRootPath?.find(
        //     (item) => location?.pathname?.indexOf(item) > -1,
        //   );
        //   Emitter.emit('HEADER_MENU_SELECT_BY_LOCATION', {
        //     rootPath: rootPath,
        //   });
        // } else {

        // }

        setSelectedHeaderMenuKey(rootPath);
      }
    }
  };

  const getCurrentSelectSubSystemByLocationParam = (location: any) => {
    if (location?.pathname) {
      let paths = location?.pathname?.split('/').filter((item) => item);
      if (paths?.length > 0) {
        let rootPath = `/${paths?.at(0)}`;
        // 做一个多层的单独判断
        if (
          Object.keys(pathNeedMultiSlash)?.findIndex(
            (item) => item === rootPath,
          ) > -1
        ) {
          let slashCount = pathNeedMultiSlash?.[rootPath];
          rootPath = '';
          for (let i = 0; i < slashCount; i++) {
            rootPath += `/${paths?.at(i)}`;
          }
        }

        // dmr 多一套判定
        if (rootPath === '/dmr') {
          if (location?.pathname?.startsWith('/dmr/examine')) {
            rootPath = '/dmr/examine';
          }
        }

        Emitter.emit('HEADER_MENU_SELECT_BY_LOCATION', {
          rootPath: rootPath,
        });

        setSelectedHeaderMenuKey(rootPath);
      }
    }
  };

  const getCurrentSelectedKeysByLocation = () => {
    let selectedKeys: string[] = [];

    if (location?.pathname) {
      selectedKeys.push(location?.pathname);
    }

    setSelectedKeys(selectedKeys);

    return selectedKeys;
  };

  useEffect(() => {
    window.addEventListener('resize', onWindowResize);
    setCollapsed(window?.innerWidth < 1280);
    return () => window.removeEventListener('resize', onWindowResize);
  }, []);

  const onWindowResize = () => {
    setCollapsed(window?.innerWidth < 1280);
  };

  const buildRouteWithQuery = (route: string, query?: object) => {
    if (query) {
      return `${route}?${qs.stringify(query)}`;
    }

    return route;
  };

  const routeInAccess = (menuRoute: string) => {
    // drg 做修改
    // 如果是highlight相关
    if (menuRoute?.includes('highlight')) {
      // console.log(props?.access, 'ininini', menuRoute);

      if (
        props?.access?.[menuRoute] !== undefined &&
        props?.access?.[menuRoute] !== null
      ) {
        // 存在自定义
        return props?.access?.[menuRoute];
      } else {
        if (menuRoute?.includes('report/highlight')) {
          return props?.access?.['/statsAnalysis/report/hospital'];
        } else if (menuRoute?.includes('chs/analysis')) {
          return props?.access?.['/chs/analysis/report'];
        } else if (menuRoute?.includes('chs/dip')) {
          return props?.access?.['/chs/dip/report'];
        }
        // 评审
        else if (menuRoute?.includes('dmr/examine')) {
          return props?.access?.['/dmr/examine/highlight/*'];
        }
      }
    }

    return props?.access?.[menuRoute];
  };

  const renderMenu = (menuData: MenuData[]) => {
    return menuData
      ?.map((item) => {
        if (!isEmptyValues(menuOrder)) {
          item['order'] = menuOrder[item['route']]?.index ?? 65535;
          item['customTitle'] = menuOrder[item['route']]?.customTitle;
        }
        return item;
      })
      ?.sort((a, b) => (a?.order ?? 65535) - (b?.order ?? 65535))
      ?.map((menuItem) => {
        if (routeInAccess(menuItem.route) && menuItem.children) {
          return (
            <SubMenu
              key={menuItem.route}
              title={menuItem.name}
              icon={menuItem.icon}
            >
              {renderMenu(menuItem.children)}
            </SubMenu>
          );
        } else {
          return routeInAccess(menuItem.route) ? (
            <Menu.Item
              key={menuItem.route}
              icon={menuItem.icon && menuItem.icon}
            >
              {leftNewTab ? (
                <Link
                  to={buildRouteWithQuery(menuItem.route, menuItem.query)}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {menuItem?.customTitle || menuItem.name}
                </Link>
              ) : (
                <Link to={buildRouteWithQuery(menuItem.route, menuItem.query)}>
                  {menuItem?.customTitle || menuItem.name}
                </Link>
              )}
            </Menu.Item>
          ) : (
            <></>
          );
        }
      });
  };

  const headerItemIds = getHeaderIds(
    props?.access,
    props?.headerMenu,
    props?.headerMenu,
  );

  // console.log('accessHeaderMenuKeys', headerItemIds);

  return (
    <>
      <div className="d-flex sub-system-container-bg">
        <Sider
          width={240}
          className={'menu-container'}
          theme={'light'}
          collapsible={true}
          collapsedWidth={75}
          breakpoint="lg"
          collapsed={isDevicePixelRatioMax() || collapsed}
          onBreakpoint={(broken) => {
            setCollapsed(broken && !collapsed);
          }}
          onCollapse={(collapsed, type) => {
            if (type === 'clickTrigger') {
              // FIXME 宽度小于 1024 不给 放大
              // if (window.innerWidth > 1024) {
              setCollapsed(collapsed);
              // }
            }
          }}
          trigger={
            isDevicePixelRatioMax() ? null : collapsed ? (
              <MenuUnfoldOutlined />
            ) : (
              <MenuFoldOutlined
                style={{ float: 'right', padding: '17px 15px' }}
              />
            )
          }
        >
          <div className="sider-logo">
            <div className={`logo-container`}>
              <img src={require('@/assets/login_logo.png')} />
            </div>
            {isDevicePixelRatioMax() || collapsed ? '' : configTitle}
          </div>

          <div className={'side-menu-container'}>
            {headerItemIds?.length > 1 ? (
              <div className="sub-system-title">
                {isDevicePixelRatioMax() || collapsed
                  ? (
                      menuOrder?.[selectedHeaderMenuKey]?.customTitle ||
                      props?.menuData?.find(
                        (item) => item.route === selectedHeaderMenuKey,
                      )?.name
                    ).at(0) || ''
                  : menuOrder?.[selectedHeaderMenuKey]?.customTitle ||
                    props?.menuData?.find(
                      (item) => item.route === selectedHeaderMenuKey,
                    )?.name}
              </div>
            ) : (
              <div className={'sub-system-empty-title'} />
            )}
            <Menu
              className="side-menu"
              mode="inline"
              theme={'light'}
              inlineIndent={8}
              // defaultOpenKeys={getCurrentOpenKeysByLocation()}
              // defaultSelectedKeys={getCurrentSelectedKeysByLocation()}
              openKeys={openedKeys}
              selectedKeys={selectedKeys}
              onClick={({ domEvent }) => {
                // domEvent.stopPropagation();
                Emitter.emit(EventConstant.MENU_CLICK);
              }}
              onOpenChange={(keys) => {
                setOpenedKeys(keys);
              }}
              onSelect={({ item, key, keyPath, selectedKeys, domEvent }) => {}}
              // selectedKeys={[]}
            >
              {renderMenu(
                props?.menuData?.find(
                  (item) => item.route === selectedHeaderMenuKey,
                )?.children || [],
              )}
            </Menu>
          </div>

          {/* <div className={'home-container'}>
            <IconHome className={'icon'} />
            <span className={'label'}>回到主页</span>
          </div> */}
        </Sider>
      </div>
    </>
  );
};

export default MenuSider;
