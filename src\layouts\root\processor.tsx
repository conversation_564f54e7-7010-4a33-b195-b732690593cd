import { isEmptyValues } from '@uni/utils/src/utils';

export const tableStripeConfiguration = () => {
  const tableStripeConfig = (window as any).externalConfig?.['common']
    ?.tableStripeConfig;

  let tableStripeStyleVariable = {};

  if (!isEmptyValues(tableStripeConfig)) {
    if (!isEmptyValues(tableStripeConfig?.oddColor)) {
      tableStripeStyleVariable['--global-tr-odd-background-color'] =
        tableStripeConfig?.oddColor;
    }

    if (!isEmptyValues(tableStripeConfig?.evenColor)) {
      tableStripeStyleVariable['--global-tr-even-background-color'] =
        tableStripeConfig?.evenColor;
    }
  }

  return tableStripeStyleVariable;
};

export const processProvinceCityDistrictData = (
  provinceData,
  cityData,
  districtData,
  subdData,
) => {
  let dataSource = {};

  let fullProv = [];
  let fullCity = [];
  let fullDistrict = [];
  let fullSubd = [];

  dataSource['86'] = {};
  provinceData?.forEach((provinceItem) => {
    // 省数据
    dataSource['86'][provinceItem?.Code] = provinceItem;
    // include full label prov
    fullProv.push({
      ...provinceItem,
      label: provinceItem?.Name,
      value: provinceItem?.Code,
    });

    // 省regex
    let provinceRegex = new RegExp(
      `^${provinceItem?.Code?.slice(0, 2)}[0-9]{2}00$`,
    );
    // 市/区数据
    dataSource[provinceItem?.Code] = {};
    cityData
      ?.filter(
        (item) =>
          item?.Code !== provinceItem?.Code && provinceRegex.test(item?.Code),
      )
      .forEach((cityItem) => {
        let newCityItem = {
          ...cityItem,
          label: cityItem?.Name,
          value: cityItem?.Code,
          fullLabel: `${provinceItem?.Name}-${cityItem?.Name}`,
          parentCodes: [provinceItem?.Code],
        };
        dataSource[provinceItem?.Code][cityItem?.Code] = newCityItem;
        fullCity.push(newCityItem);

        // 市/区regex
        let cityRegex = new RegExp(`^${cityItem?.Code?.slice(0, 4)}[0-9]{2}$`);
        // 县/区数据
        dataSource[cityItem?.Code] = {};
        districtData
          ?.filter(
            (item) =>
              item?.Code !== cityItem?.Code && cityRegex.test(item?.Code),
          )
          .forEach((districtItem) => {
            let newDistrictItem = {
              ...districtItem,
              label: districtItem?.Name,
              value: districtItem?.Code,
              fullLabel: `${provinceItem?.Name}-${cityItem?.Name}-${districtItem?.Name}`,
              parentCodes: [provinceItem?.Code, cityItem?.Code],
            };

            dataSource[cityItem?.Code][districtItem?.Code] = newDistrictItem;
            fullDistrict.push(newDistrictItem);

            // 街道
            if (!isEmptyValues(subdData)) {
              let subdRegex = new RegExp(`^${districtItem?.Code}[0-9]{3}$`);
              subdData
                ?.filter((item) => subdRegex.test(item?.Code))
                ?.forEach((subdItem) => {
                  let newSubdItem = {
                    ...subdItem,
                    label: subdItem?.Name,
                    value: subdItem?.Code,
                    fullLabel: `${provinceItem?.Name}-${cityItem?.Name}-${districtItem?.Name}-${subdItem?.Name}`,
                    parentCodes: [
                      provinceItem?.Code,
                      cityItem?.Code,
                      districtItem?.Code,
                    ],
                  };

                  dataSource[cityItem?.Code][districtItem?.Code][
                    subdItem?.Code
                  ] = newSubdItem;
                  fullSubd.push(newSubdItem);
                });
            }
          });
      });
  });

  // console.log('processProvinceCityDistrictData', dataSource);

  return {
    ProvCityCoty: dataSource,
    ProvFull: fullProv,
    CityFull: fullCity,
    DistrictFull: fullDistrict,
    SubdFull: fullSubd,
  };
};
