import React, { useEffect } from 'react';
import RootLayout from '@/layouts/root/RootLayout';
import { useModel } from '@@/plugin-model/useModel';

export default function Layout(props: any) {
  const { initialState }: any = useModel('@@initialState');

  // TODO globalState 的useEffect 刷新时机 会影响到userInfo 放入globalState中 因此此处暂时使用sessionStorage 来保存用户信息
  useEffect(() => {
    sessionStorage.setItem('userInfo', JSON.stringify(initialState?.userInfo));
  }, [initialState?.userInfo]);

  useEffect(() => {
    sessionStorage.setItem(
      'configurationInfo',
      JSON.stringify(initialState?.configurationInfo ?? {}),
    );
  }, [initialState?.configurationInfo]);
  console.log('initialState', initialState);
  return (
    <>
      <RootLayout {...props}>{props.children}</RootLayout>
    </>
  );
}
