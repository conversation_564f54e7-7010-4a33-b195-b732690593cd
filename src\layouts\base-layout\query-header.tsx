import { Col, Form, FormInstance, Row, Tooltip } from 'antd';
import { dynamicComponentsMap } from '@/utils/dynamicComponents';
import React from 'react';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  BaseLayoutHeaderItem,
  BaseLayoutHeaderProps,
} from '@/layouts/base-layout/index';

interface QueryHeaderFormProps extends BaseLayoutHeaderProps {
  form: FormInstance;
  headerDataSource: any;
  errorFields?: any;
  onHeaderItemChange: (value: any[], key: string) => void;
  onHeaderItemChangeOnMultipleValues: (values: any[], dataKey: string) => void;
}

const QueryHeaderForm = (props: QueryHeaderFormProps) => {
  let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

  return (
    <Col span={18}>
      <Row gutter={[16, 16]}>
        {props.items?.map((headerItem: BaseLayoutHeaderItem) => {
          const DynamicComponent = dynamicComponentsMap[
            `Uni${headerItem?.componentName}`
          ] as React.FC;

          let headerSearchValue = props?.form?.getFieldsValue();

          // 加一个valueKeys list
          let value = headerItem?.props?.valueKeys
            ? headerItem?.props?.valueKeys?.map((d) => headerSearchValue[d])
            : headerSearchValue[
                headerItem?.props?.valueKey || headerItem?.props?.dataKey
              ];

          if (headerItem?.props && headerItem?.props?.propsPreProcess) {
            headerItem.props = headerItem?.props?.propsPreProcess(
              headerSearchValue,
              headerItem?.props,
            );
          }

          let dataSource = cloneDeep(
            headerItem?.props?.dataKeyGroup
              ? props?.headerDataSource?.[headerItem?.props?.dataKeyGroup]?.[
                  headerItem?.props?.dataKey
                ]?.slice()
              : props?.headerDataSource[headerItem?.props?.dataKey]?.slice(),
          );
          if (headerItem?.props && headerItem?.props?.dataSourcePreProcess) {
            dataSource =
              headerItem?.props?.dataSourcePreProcess?.length > 2
                ? headerItem?.props?.dataSourcePreProcess(
                    headerSearchValue,
                    dataSource,
                    userInfo,
                  )
                : headerItem?.props?.dataSourcePreProcess(
                    headerSearchValue,
                    dataSource,
                  );
          }

          // 最后等dataSoure好了 再处理特殊情况给定的默认值
          if (
            headerItem?.props?.defaultValueType &&
            (!value || value?.length < 1)
          ) {
            switch (headerItem?.props?.defaultValueType) {
              case 'firstValueSingle':
                value = dataSource?.at(0)?.Code; // TODO 修改
                console.log(
                  'firstValueSingle',
                  props?.form?.getFieldsValue(),
                  dataSource,
                );
                props?.form?.setFieldValue(
                  headerItem?.props?.valueKey,
                  dataSource?.at(0)?.Code,
                );
                break;
              case 'byUserInfo':
                break;
              default:
                break;
            }
          }

          let formItemProps = cloneDeep(headerItem?.props?.formProps) ?? {};
          if (headerItem?.required) {
            formItemProps['rules'] = [
              ...(headerItem?.props?.formProps?.rules ?? []),
              {
                required: true,
                message: `请填写${headerItem?.label}`,
              },
            ];
          }

          let ErrorTooltipWrapper = (props: any) => <>{props?.children}</>;
          let tooltipProps: any = {
            title: '',
          };
          if (headerItem?.props?.errorTooltip === true) {
            let errorFieldItem = props?.errorFields?.find(
              (errorItem) =>
                errorItem?.name?.join(',') ===
                (headerItem?.props?.valueKey ?? headerItem?.props?.dataKey),
            );
            if (errorFieldItem !== undefined) {
              tooltipProps = {
                placement: 'topLeft',
                title: errorFieldItem?.errors?.join(','),
              };
            }
            ErrorTooltipWrapper = Tooltip;
          }

          if (headerItem?.props?.placeholder === '请选择科室（基于院区）') {
            console.log(headerItem, headerItem?.props);
          }

          if (headerItem?.componentName === 'Switch') {
            formItemProps['valuePropName'] = 'checked';
          }

          return (
            <Col
              className={'header-item-container'}
              xs={24}
              sm={24}
              md={12}
              lg={12}
              xl={12}
              key={headerItem?.props?.valueKey}
            >
              {headerItem?.label && headerItem?.outerLabelHidden !== true && (
                <label
                  className={
                    headerItem?.props?.requiredStatus &&
                    'ant-form-item-required-like'
                  }
                >
                  {headerItem?.required && (
                    <span className={'require-mark'}>*</span>
                  )}
                  {headerItem?.label}：
                </label>
              )}
              <ErrorTooltipWrapper {...tooltipProps}>
                <Form.Item
                  {...formItemProps}
                  name={
                    headerItem?.props?.formKey ??
                    headerItem?.props?.valueKey ??
                    headerItem?.props?.dataKey
                  }
                  validateTrigger={'onFinish'}
                  // valuePropName={'formValue'}
                >
                  <DynamicComponent
                    className={`header-item-base ${headerItem?.props?.className}`}
                    {...headerItem?.props}
                    form={props?.form}
                    getPopupContainer={(trigger) =>
                      trigger?.parentElement || document.body
                    }
                    selectorHideInValid={
                      headerItem?.props?.selectorHideInValid ?? true
                    }
                    status={
                      props?.errorFields?.find(
                        (errorItem) =>
                          errorItem?.name?.join(',') ===
                          (headerItem?.props?.valueKey ??
                            headerItem?.props?.dataKey),
                      )
                        ? 'error'
                        : undefined
                    }
                    extra={{
                      form: props?.form,
                      radioFormKey: headerItem?.props?.valueKeys?.at(0),
                      valueKeys: headerItem?.props?.valueKeys,
                      required: headerItem?.required ?? false,
                      label: headerItem?.label,
                    }}
                    dataSource={dataSource}
                    // value={
                    //   value && headerItem?.props?.valuePreProcess
                    //     ? headerItem?.props?.valuePreProcess(value)
                    //     : value
                    // }
                    checked={
                      !isEmptyValues(value) &&
                      headerItem?.props?.valuePreProcess
                        ? headerItem?.props?.valuePreProcess(value)
                        : value
                    }
                    mousedownOptionOpen={true}
                    onChange={(value) => {
                      let dataKey =
                        headerItem?.props?.valueKey ||
                        headerItem?.props?.dataKey;

                      if (headerItem?.props?.postProcess) {
                        value = headerItem?.props?.postProcess(
                          headerSearchValue[dataKey],
                          value,
                          headerItem?.componentName === 'RangePicker'
                            ? { picker: headerItem?.props?.picker }
                            : {},
                        );
                      }

                      props?.onHeaderItemChange(value, dataKey);
                    }}
                    // 加一个专门处理 valueKeys 的 onChange
                    onChangeValueKeys={(values, key = undefined) => {
                      let dataKey =
                        headerItem?.props?.valueKey ||
                        headerItem?.props?.dataKey;

                      if (headerItem?.props?.postProcess) {
                        values = headerItem?.props?.postProcess(
                          headerSearchValue,
                          values,
                          headerItem?.componentName === 'DateRadioPicker'
                            ? {
                                valueKeys: headerItem?.props?.valueKeys,
                                picker: 'byValue',
                              }
                            : {},
                        );
                      }
                      props?.onHeaderItemChangeOnMultipleValues(
                        values,
                        dataKey,
                      );
                    }}
                  />
                </Form.Item>
              </ErrorTooltipWrapper>
            </Col>
          );
        })}
      </Row>
    </Col>
  );
};

export default QueryHeaderForm;
