import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { UniTable } from '@uni/components/src';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { ColumnItem, RespVO } from '@uni/commons/src/interfaces';
import {
  ReportMasterItem,
  ReportBaseInfo,
  ReportDetailItem,
  ReportItem,
  TableBaseProps,
  ValidationItem,
} from '@/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { ReportEventConstant } from '@/constants';
import {
  Card,
  Button,
  message,
  notification,
  Pagination,
  TableProps,
  Space,
  Tooltip,
  Divider,
} from 'antd';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  ARCHIVE,
  columns,
  DOWNLOAD,
  EXPORT,
  EXPORT_DOWNLOAD,
  LOAD,
  LOCK,
  REFRESH,
  ROW_ADD,
  UNLOCK,
} from '@/tables/constants';
import {
  archiveReport,
  asyncValidateReport,
  loadReport,
  reloadReport,
  syncValidateReport,
} from '@/tables/network';
import {
  downloadReportByBlobId,
  masterItemExtraConfig,
  reportTableGroupNameHeaderProcessor,
  showNotification,
  transformReportColumnsWithDataTypeIntOrderable,
} from '@/utils';
import { debounce } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import UniEditableTable from '@uni/components/src/table/edittable';
import isNil from 'lodash/isNil';
import dayjs from 'dayjs';
import reportBase from '@/components/left-menu/report-base';
import { ValidationResult, ValidationResultDetails } from '@/tables/validation';
import { downloadFile } from '@uni/utils/src/download';
import UniPagination from '@uni/components/src/table/pagination';
import { useModel } from 'umi';
import { useAntdResizableHeader } from '@uni/components/src/table/resizable-column/header';
import { PushpinOutlined } from '@ant-design/icons';
import { commonBusinessDomain } from '@uni/services/src/commonService';
import { isEmptyValues } from '@uni/utils/src/utils';
import PivotTransformService, { hasPivotColumn } from '@/tables/pivot';

const messageKey = 'exporting';

interface DetailsReportPersistTableProps extends TableBaseProps {}

const pivotServiceInstance = new PivotTransformService();

const DetailsReportPersistTable = (props: DetailsReportPersistTableProps) => {
  const actionRef = useRef<any>();

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [reportLoading, setReportLoading] = useState(false);

  const [reportRefreshLoading, setReportRefreshLoading] = useState(false);

  const [reportExportLoading, setReportExportLoading] = useState(false);

  const [reportBaseInfo, setReportBaseInfo] =
    useState<ReportBaseInfo>(undefined);

  // load base info
  const [reportLoadDataBaseInfo, setReportLoadDataBaseInfo] =
    useState<ReportBaseInfo>(undefined);

  const [reportArchiveBaseInfo, setReportArchiveBaseInfo] =
    useState<ReportBaseInfo>(undefined);

  const [reportValidationBaseInfo, setReportValidationBaseInfo] =
    useState<ReportBaseInfo>(undefined);

  const [reportReLoadDataBaseInfo, setReportReLoadDataBaseInfo] =
    useState<ReportBaseInfo>(undefined);

  const [reportTableDataSource, setReportTableDataSource] = useState([]);
  const [reportTableColumns, setReportTableColumns] = useState([]);

  const [reportTableExtraColumns, setReportTableExtraColumns] = useState([]);

  const [reportItem, setReportItem] = useState<ReportItem>(undefined);
  const [masterItem, setMasterItem] = useState<ReportMasterItem>(undefined);

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  const [validationFailure, setValidationFailure] = useState(false);

  const [isReloadReport, setIsReloadReport] = useState(false);

  const [reportValidateFailureOpen, setReportValidateFailureOpen] =
    useState(false);

  const [existExportedFileInfo, setExistExportedFileInfo] = useState(undefined);

  const { components, resizableColumns, tableWidth, resetColumns } =
    useAntdResizableHeader({
      columns: React.useMemo(() => {
        return reportTableColumns;
      }, [reportTableColumns]),
    });

  /**
   * 后端分页 start
   */
  const [backPagination, setBackPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    pageSizeOptions: ['10', '20', '30', '50'],
  });

  // 后端分页OnChange
  const backTableOnChange: TableProps<any>['onChange'] = (pagination) => {
    setReportLoading(true);
    setBackPagination({
      ...backPagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });

    reportDataDetailReq(
      reportBaseInfo?.ReportSettingMasterId,
      reportBaseInfo?.ReportBaseId,
      pagination.current,
      pagination.pageSize,
    );
  };
  /**
   * 后端分页 end
   */

  useEffect(() => {
    // 监听 validation extraProperties
    Emitter.on(
      ReportEventConstant.REPORT_VALIDATION_ERROR_DATA_EXIST,
      (count) => {
        setReportValidateFailureOpen(true);
      },
    );

    (global?.window as any)?.eventEmitter?.on('REPORT_FILE_ID', (data) => {
      setExistExportedFileInfo(data);
    });

    // 卸载的时候停止刷新
    return () => {
      Emitter.off(ReportEventConstant.REPORT_VALIDATION_ERROR_DATA_EXIST);

      // 取消轮询
      stopPolling();
      Emitter.off(ReportEventConstant.REPORT_ITEM_CLICK);
      (global?.window as any)?.eventEmitter?.off('REPORT_FILE_ID');
    };
  }, []);

  // 用于设定refreshLoading 当reportLoading = false的时候同步设定refreshLoading 为false
  useEffect(() => {
    if (reportLoading === false) {
      setReportRefreshLoading(false);
    }
  }, [reportLoading]);

  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_ITEM_CLICK, async (data) => {
      if (
        data?.masterItem === undefined ||
        data?.reportItem === undefined ||
        (data?.masterItem?.Id === masterItem?.Id &&
          data?.reportItem?.ReportBaseId === reportItem?.ReportBaseId)
      ) {
        return;
      }

      // 停止轮询
      stopPolling();
      // report item & master item
      setReportItem(data?.reportItem);

      // 解析ExtraConfig
      let extraConfigs = masterItemExtraConfig(masterItem?.ExtraConfig);
      if (!isEmptyValues(extraConfigs?.extraColumns)) {
        setReportTableExtraColumns(extraConfigs?.extraColumns ?? []);
      }

      setMasterItem(data?.masterItem);
      // reset
      resetState();
      setValidationFailure(false);
      // 重置validationData
      Emitter.emit(ReportEventConstant.VALIDATION_DATA_RESET);

      setReportLoading(true);
      // report base info
      let reportInfo = await reportBaseInfoReq(
        data?.masterItem?.Id,
        data?.reportItem?.ReportBaseId,
      );

      if (data?.masterItem?.DataSize === 'Details' && reportInfo) {
        if (reportInfo?.ReportStatus === '2') {
          setIsReloadReport(false);
          getReportDataWithReportStatusEqualsTwo(reportInfo);
        } else if (reportInfo?.ReportStatus === '0') {
          message.success('暂无数据。开始加载数据');

          let loadReportResponse: RespVO<any> = await loadReport(
            data?.masterItem?.Id,
            data?.reportItem?.ReportBaseId,
          );
          if (
            loadReportResponse?.code === 0 &&
            loadReportResponse?.statusCode === 200
          ) {
            reportBaseInfoDataLoadReq(
              data?.masterItem?.Id,
              data?.reportItem?.ReportBaseId,
            );
          } else {
            message.error('数据加载出错，请稍后重试');
          }
        } else if (reportInfo?.ReportStatus === '1') {
          // 正在加载
          message.warn(reportInfo?.ReportStatusName);
          // 启动轮询
          reportBaseInfoDataLoadReq(
            data?.masterItem?.Id,
            data?.reportItem?.ReportBaseId,
          );
        } else if (reportInfo?.ReportStatus === '999') {
          setReportLoading(false);
          message.error(reportInfo?.ReportStatusName);
        }
      }
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_ITEM_CLICK);
    };
  }, [reportItem, masterItem]);

  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_ITEM_UPDATED, async (values) => {
      message.success('保存中....');
      let updateResponse = await reportDataUpdateReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
        values,
      );

      if (updateResponse?.code === 0 && updateResponse?.statusCode === 200) {
        setReportTableDataSource(values);
        message.error('保存成功');
      } else {
        message.error('保存失败');
      }
    });

    // 导出 走新模式 老模式先注
    Emitter.on(ReportEventConstant.REPORT_EXPORT, async () => {
      // message.success(`报表： ${reportBaseInfo?.Title}导出中，请稍后`);
      // setReportExportLoading(true);
      // let exportName = `${reportBaseInfo?.Title}-${dayjs().format(
      //   'YYYYMMDD_HHmmss',
      // )}`;

      // if (queryDetailExportResponse?.response) {
      //   message.success(`报表： ${masterItem?.Title}导出成功`);
      //   downloadFile(exportName, queryDetailExportResponse?.response);
      // } else {
      //   message.success(`报表： ${masterItem?.Title}导出失败，请联系管理员`);
      // }
      // setReportExportLoading(false);

      exportStart({
        ReportSettingMasterId: reportBaseInfo?.ReportSettingMasterId,
        ReportBaseId: reportBaseInfo?.ReportBaseId,
      });
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_ITEM_UPDATED);
      Emitter.off(ReportEventConstant.REPORT_EXPORT);
    };
  }, [reportTableDataSource, reportBaseInfo]);

  // export new mode
  const [backendLoading, setBackendLoading] = useState(false);

  const {
    data: exportStartData,
    loading: exportStartLoading,
    run: exportStart,
  } = useRequest(
    (data) => {
      setReportExportLoading(true);
      return uniCommonService('Api/Report/Report/ExportQueryDetails', {
        method: 'POST',
        data: {
          // 默认
          DtParam: {
            Draw: 1,
            Start: 0,
            Length: 2147483647,
          },
          ...data,
        },
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => res,
      onSuccess(res, params) {
        console.log(res);
        if (res.response?.status === 202) {
          exportCheck(res.data.Id);
          message.loading({
            content: '正在导出...',
            key: messageKey,
            duration: 0,
          });
        } else {
        }
      },
    },
  );
  // 轮询 export check
  const {
    data: exportRecord,
    loading: exportCheckLoading,
    run: exportCheck,
    cancel: cancelExportCheck,
  } = useRequest(
    (id) => {
      return uniCommonService('Api/Common/ExportCenter/GetExportRecord', {
        method: 'POST',
        params: { Id: id },
      });
    },
    {
      manual: true,
      pollingInterval: 1000,
      pollingWhenHidden: true,
      //   formatResult: (res: RespVO<any>) => res,
      onSuccess(res, params) {
        if (res?.Status === '100') {
          cancelExportCheck();

          setReportExportLoading(false);
          message.destroy(messageKey);
          message.success('导出成功');

          let a = document.createElement('a');
          a.target = '_blank';
          a.href = `${commonBusinessDomain}/Api/Common/Blob/Download?Id=${res.BlobId}`;
          a.download = `${reportBaseInfo?.Title}-${dayjs().format(
            'YYYYMMDD_HHmmss',
          )}`;
          //   document.body.appendChild(a);
          a.click();
          //   document.body.removeChild(a);
        } else {
        }
      },
    },
  );

  // refresh load archive
  useEffect(() => {
    Emitter.on(
      ReportEventConstant.REPORT_DOWNLOAD,
      debounce(
        () => {
          onDownloadClick();
        },
        1000,
        {
          leading: true,
          trailing: false,
        },
      ),
    );

    Emitter.on(ReportEventConstant.REPORT_REFRESH, async () => {
      setReportLoading(true);
      setReportRefreshLoading(true);
      setReportTableDataSource([]);
      // 重置 existExportedFileId
      setExistExportedFileInfo(undefined);

      Emitter.emit(ReportEventConstant.VALIDATION_DATA_RESET);
      setValidationFailure(false);
      let reloadResponse = await reloadReport(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
      );
      message.success('重新加载成功，请等待加载完成');
      // TODO 启动轮询
      if (reloadResponse?.code === 0 && reloadResponse?.statusCode === 200) {
        reportBaseInfoDataReLoadReq(
          reportBaseInfo?.ReportSettingMasterId,
          reportBaseInfo?.ReportBaseId,
        );
      } else {
        setReportLoading(false);
      }
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_DOWNLOAD);
      Emitter.off(ReportEventConstant.REPORT_REFRESH);
    };
  }, [reportBaseInfo]);

  // 锁定 解锁 下载
  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_LOCK, async () => {
      let reportLockResponse = await reportBaseInfoDataLockReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
      );

      if (
        reportLockResponse?.code === 0 &&
        reportLockResponse?.statusCode === 200
      ) {
        reportBaseInfoReq(
          reportBaseInfo?.ReportSettingMasterId,
          reportBaseInfo?.ReportBaseId,
        );
      }
    });

    Emitter.on(ReportEventConstant.REPORT_UNLOCK, async () => {
      let reportUnLockResponse = await reportBaseInfoDataUnlockReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
      );

      if (
        reportUnLockResponse?.code === 0 &&
        reportUnLockResponse?.statusCode === 200
      ) {
        reportBaseInfoReq(
          reportBaseInfo?.ReportSettingMasterId,
          reportBaseInfo?.ReportBaseId,
        );
      }
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_LOCK);
      Emitter.off(ReportEventConstant.REPORT_UNLOCK);
    };
  }, [reportBaseInfo]);

  // load data
  useEffect(() => {
    if (reportLoadDataBaseInfo?.ReportStatus === '2') {
      reportBaseInfoDataLoadCancel();
      setReportLoading(false);

      setIsReloadReport(false);
      getReportDataWithReportStatusEqualsTwo(reportLoadDataBaseInfo);

      setReportBaseInfo(reportLoadDataBaseInfo);
    } else if (reportLoadDataBaseInfo?.ReportStatus === '999') {
      message.error(reportLoadDataBaseInfo?.ReportStatusName);
      stopPolling();
      setReportLoading(false);
    } else if (reportLoadDataBaseInfo?.ReportStatus === '0') {
      message.warn(reportLoadDataBaseInfo?.ReportStatusName);
      stopPolling();
      setReportLoading(false);
    }
  }, [reportLoadDataBaseInfo]);

  // reload data
  useEffect(() => {
    if (reportReLoadDataBaseInfo?.ReportStatus === '2') {
      reportBaseInfoDataReLoadCancel();
      setReportLoading(false);

      setIsReloadReport(true);
      getReportDataWithReportStatusEqualsTwo(reportReLoadDataBaseInfo);
    } else if (reportReLoadDataBaseInfo?.ReportStatus === '999') {
      reportBaseInfoDataReLoadCancel();
      setReportLoading(false);
      message.error(reportReLoadDataBaseInfo?.ReportStatusName);
    }

    setReportBaseInfo(reportReLoadDataBaseInfo);
  }, [reportReLoadDataBaseInfo]);

  // archive data
  useEffect(() => {
    if (reportArchiveBaseInfo) {
      if (props?.masterItem?.EnableArchive) {
        if (reportArchiveBaseInfo?.ReportArchiveStatus === '100') {
          reportBaseInfoArchiveCancel();
          // 请求数据
          getReportTableData();
        } else if (reportArchiveBaseInfo?.ReportArchiveStatus === '999') {
          setReportLoading(false);
          reportBaseInfoArchiveCancel();
          message.error('归档出错，请联系管理员');
          return;
        }
      } else {
        getReportTableData();
      }

      setReportBaseInfo(reportArchiveBaseInfo);
    }
  }, [reportArchiveBaseInfo]);

  // validate data
  useEffect(() => {
    if (reportValidationBaseInfo) {
      if (props?.masterItem?.EnableValidate) {
        if (reportValidationBaseInfo?.ReportValidateStatus === '99') {
          reportBaseInfoValidationCancel();
          // 校验不通过
          setValidationFailure(true);
          // 捞表格数据
          getReportTableData();
        } else if (reportValidationBaseInfo?.ReportValidateStatus === '100') {
          reportBaseInfoValidationCancel();
          setReportLoading(false);
          Emitter.emit(ReportEventConstant.REPORT_VALIDATION_FETCH);
          // 校验通过
          archiveReportAfterValidation();
          return;
        } else if (reportValidationBaseInfo?.ReportValidateStatus === '999') {
          reportBaseInfoValidationCancel();
          setReportLoading(false);
          message.error('报表校验出错，请联系管理员');
          return;
        }
      } else {
        archiveReportAfterValidation();
      }

      setReportBaseInfo(reportValidationBaseInfo);
    }
  }, [reportValidationBaseInfo, reportTableExtraColumns]);

  const getReportDataWithReportStatusEqualsTwo = async (
    reportBaseInfo: ReportBaseInfo,
  ) => {
    setReportLoading(true);
    // 先validate
    if (props?.masterItem?.EnableValidate) {
      if (isReloadReport || reportBaseInfo?.ReportValidateStatus === '0') {
        // 未启动审核的时候才审核
        message.loading('报表数据审核中');
        let syncValidateResponse: RespVO<ValidationItem[]> =
          await asyncValidateReport(
            reportBaseInfo?.ReportSettingMasterId,
            reportBaseInfo?.ReportBaseId,
          );

        if (
          syncValidateResponse?.code !== 0 ||
          syncValidateResponse?.statusCode !== 200
        ) {
          message.error('报表数据校验失败，请稍后重试');
          setReportLoading(false);
          return;
        }
      }

      reportBaseInfoValidationReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
      );
    } else {
      setReportValidationBaseInfo(reportBaseInfo);
    }
  };

  const archiveReportAfterValidation = async () => {
    // 再归档
    if (props?.masterItem?.EnableArchive) {
      if (isReloadReport || reportBaseInfo?.ReportArchiveStatus !== '100') {
        let archiveResponse = await archiveReport(
          reportBaseInfo?.ReportSettingMasterId,
          reportBaseInfo?.ReportBaseId,
        );
        if (
          archiveResponse?.code !== 0 ||
          archiveResponse?.statusCode !== 200
        ) {
          message.error('归档发生错误，请稍后重试');
          setReportLoading(false);
          // setReportDownloadClicked(false);
        }
      }

      // 轮询启动
      reportBaseInfoArchiveReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
      );
    } else {
      setReportArchiveBaseInfo(reportBaseInfo);
    }
  };

  const getReportTableData = () => {
    let currentPagination = Object.assign({}, backPagination);
    currentPagination.current = 1;
    currentPagination.pageSize = 10;

    reportDataDetailReq(
      reportBaseInfo?.ReportSettingMasterId,
      reportBaseInfo?.ReportBaseId,
      1,
      10,
    );
    setBackPagination({
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['10', '20', '30', '50'],
    });
  };

  const resetState = () => {
    //reset
    setReportTableDataSource([]);
    setReportTableColumns([]);
    setBackPagination({
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['10', '20', '30', '50'],
    });
  };

  const processOperationBtns = () => {
    let btns = [];

    if (reportBaseInfo?.ReportStatus === '2') {
      if (reportBaseInfo?.IsLocked === false) {
        // 锁定
        btns.push(LOCK(reportLoading));
      }

      if (reportBaseInfo?.IsLocked) {
        btns.push(UNLOCK(reportLoading));
      }

      // btns.push(EXPORT(reportLoading, reportExportLoading));

      // // 下载
      // if (props?.masterItem?.EnableArchive) {
      //   btns.push(
      //     DOWNLOAD(reportTableDataSource?.length === 0 || validationFailure),
      //   );
      // }
    }

    // 保证选择了才会出现加载按钮
    if (reportBaseInfo?.ReportStatus === '0' && reportItem?.ReportBaseId) {
      btns.push(LOAD(reportLoading));
    }

    if (
      reportBaseInfo &&
      reportBaseInfo?.ReportStatus !== '0' &&
      reportBaseInfo?.IsLocked === false
    ) {
      // 刷新
      btns.push(REFRESH(reportLoading, reportRefreshLoading));
    }

    btns.push(<Divider type="vertical" />);

    // 归档于导出 合并为   PersistDetails皆调用ExportQueryDetails
    if (reportBaseInfo?.ReportStatus === '2') {
      if (props?.masterItem?.EnableExport && props?.masterItem?.EnableArchive) {
        btns.push(
          EXPORT_DOWNLOAD(
            reportLoading,
            {
              loading: reportLoading,
              isBackend: true,
              backendData: {
                ReportSettingMasterId: reportBaseInfo?.ReportSettingMasterId,
                ReportBaseId: reportBaseInfo?.ReportBaseId,
              },
            },
            {
              disabled:
                reportTableDataSource?.length === 0 || validationFailure,
              btnText: props?.masterItem?.ArchiveDescription,
            },
            existExportedFileInfo,
          ),
        );
      } else {
        btns.push(
          EXPORT_DOWNLOAD(
            reportLoading,
            {
              loading: reportLoading,
              isBackend: true,
              backendData: {
                ReportSettingMasterId: reportBaseInfo?.ReportSettingMasterId,
                ReportBaseId: reportBaseInfo?.ReportBaseId,
              },
            },
            {
              disabled: true,
              btnText: props?.masterItem?.ArchiveDescription,
            },
            existExportedFileInfo,
          ),
        );
      }
    }

    // 最后添加 错误报告 打开/关闭 按钮
    btns.push(
      <Tooltip title="错误报告">
        <Button
          type="text"
          shape="circle"
          icon={<PushpinOutlined />}
          onClick={() => {
            setReportValidateFailureOpen(!reportValidateFailureOpen);
          }}
        />
      </Tooltip>,
    );

    return btns;
  };

  const onDownloadClick = async () => {
    // 先归档
    if (reportBaseInfo?.LatestArchive) {
      downloadReportByBlobId(
        reportBaseInfo?.LatestArchive,
        `${reportBaseInfo?.Title}-${dayjs().format('YYYYMMDD_HHmmss')}`,
      );
      stopPolling();
    } else {
      message.error('归档出现错误，暂不支持下载');
      // TODO 归档错误 ？
    }
  };

  const stopPolling = () => {
    reportBaseInfoArchiveCancel();
    reportBaseInfoDataLoadCancel();
    reportBaseInfoDataReLoadCancel();
  };

  // reportBaseInfo
  const { run: reportBaseInfoReq } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/GetReportBaseById', {
        method: 'POST',
        data: {
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ReportBaseInfo>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setReportBaseInfo(response?.data);
          return response?.data;
        } else {
          setReportBaseInfo({});
          return null;
        }
      },
    },
  );

  // report data update
  const { loading: reportDataUpdateLoading, run: reportDataUpdateReq } =
    useRequest(
      (masterId, reportId, tableData) => {
        return uniCommonService('Api/Report/Report/Update', {
          method: 'POST',
          data: {
            ReportBaseId: reportId,
            reportSettingMasterId: masterId,
            data: tableData,
          },
          requestType: 'json',
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<any>) => {
          return response;
        },
      },
    );

  // columns
  const { loading: reportColumnsLoading, run: reportColumnsReq } = useRequest(
    (id) => {
      return uniCommonService('Api/Report/Report/GetReportDataTablesColumns', {
        params: {
          ReportSettingMasterId: id,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let transformedTableColumns = reportTableExtraColumns?.concat(
            reportTableGroupNameHeaderProcessor(
              tableColumnBaseProcessor(
                masterItem?.IsReadonly ? [] : columns,
                transformReportColumnsWithDataTypeIntOrderable(
                  masterItem?.ReportMode,
                  response?.data,
                ),
              ).map((item) => {
                return {
                  ...item,
                  readonly: masterItem?.IsReadonly || item?.isReadOnly,
                  formItemProps: {
                    rules: [
                      {
                        required: !item?.isNullable,
                        whitespace: false,
                        message: '此项是必填项',
                      },
                    ],
                  },
                };
              }),
            ),
          );

          if (hasPivotColumn(transformedTableColumns)) {
            pivotServiceInstance.setTableColumns(transformedTableColumns);
            setReportTableColumns([]);
          } else {
            setReportTableColumns(transformedTableColumns);
          }
        } else {
          setReportTableColumns([]);
        }
      },
    },
  );

  const { run: reportBaseInfoArchiveReq, cancel: reportBaseInfoArchiveCancel } =
    useRequest(
      (masterId, reportId) => {
        return uniCommonService('Api/Report/Report/GetReportBaseById', {
          method: 'POST',
          data: {
            ReportBaseId: reportId,
          },
          requestType: 'json',
        });
      },
      {
        manual: true,
        pollingInterval: 1000,
        pollingWhenHidden: false,
        formatResult: async (response: RespVO<ReportBaseInfo>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setReportArchiveBaseInfo(response?.data);
          }
        },
      },
    );

  const {
    run: reportBaseInfoValidationReq,
    cancel: reportBaseInfoValidationCancel,
  } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/GetReportBaseById', {
        method: 'POST',
        data: {
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      pollingInterval: 1000,
      pollingWhenHidden: false,
      formatResult: async (response: RespVO<ReportBaseInfo>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setReportValidationBaseInfo(response?.data);
        }
      },
    },
  );

  const {
    run: reportBaseInfoDataLoadReq,
    cancel: reportBaseInfoDataLoadCancel,
  } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/GetReportBaseById', {
        method: 'POST',
        data: {
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      pollingInterval: 1000,
      pollingWhenHidden: false,
      formatResult: async (response: RespVO<ReportBaseInfo>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setReportLoadDataBaseInfo(response?.data);
        }
      },
    },
  );

  const { run: reportBaseInfoDataLockReq } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/Lock', {
        method: 'POST',
        data: {
          ReportSettingMasterId: masterId,
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        return response;
      },
    },
  );

  const { run: reportBaseInfoDataUnlockReq } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/UnLock', {
        method: 'POST',
        data: {
          ReportSettingMasterId: masterId,
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        return response;
      },
    },
  );

  const {
    run: reportBaseInfoDataReLoadReq,
    cancel: reportBaseInfoDataReLoadCancel,
  } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/GetReportBaseById', {
        method: 'POST',
        data: {
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      pollingInterval: 1000,
      pollingWhenHidden: false,
      formatResult: async (response: RespVO<ReportBaseInfo>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setReportReLoadDataBaseInfo(response?.data);
        }
      },
    },
  );

  const { run: reportDataDetailReq } = useRequest(
    (masterId, reportId, current, pageSize) => {
      if (reportTableColumns?.length === 0) {
        reportColumnsReq(masterId);
      }

      let data = {
        ReportSettingMasterId: masterId,
        ReportBaseId: reportId,
      };

      data['DtParam'] = {
        Start: (current - 1) * pageSize,
        Length: pageSize,
      };

      return uniCommonService('Api/Report/Report/QueryDetails', {
        method: 'POST',
        data: data,
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ReportDetailItem>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          if (pivotServiceInstance?.hasPivotColumns === true) {
            let pivotData = pivotServiceInstance.pivotColumnsDataTransformer(
              response?.data?.data,
              [],
            );

            setReportTableColumns(pivotData?.columns);
            setReportTableDataSource(pivotData?.dataSources ?? []);
          } else {
            setReportTableDataSource(
              response?.data?.data?.map((item) => {
                return {
                  rowId: uuidv4(),
                  ...item,
                };
              }),
            );
          }

          setBackPagination({
            ...backPagination,
            total: response?.data?.recordsTotal || 0,
          });
        } else {
          setReportTableDataSource([]);
        }
        setReportLoading(false);
      },
    },
  );

  const tableY =
    document.getElementById('report-content-container')?.offsetHeight -
    (document.getElementsByClassName('ant-table-thead')?.[0]?.clientHeight ||
      0) -
    64 -
    64 -
    (document.getElementById('report-title')?.offsetHeight || 0) -
    20 -
    10 -
    45;

  return (
    <div
      id={'detail-table-persist-container'}
      className={'detail-table-persist-container'}
    >
      {/* <span id={'report-title'} className={'title'}>
        {reportBaseInfo?.Title}
      </span> */}
      <div className={'flex-row'} style={{ flex: 1 }}>
        {/* <div
          id={'report-content-container'}
          className={'report-content-container'}
          style={{
            width: `${reportValidateFailureOpen ? '60%' : 'calc(100% - 40px)'}`,
          }}
        >
        </div> */}
        <Card
          id={'report-content-container'}
          className={'report-content-container'}
          title={reportBaseInfo?.Title}
          style={{
            width: `${reportValidateFailureOpen ? '60%' : 'calc(100% - 40px)'}`,
          }}
          extra={
            <>
              <Space>{processOperationBtns()}</Space>
            </>
          }
        >
          <UniTable
            id={'report-table'}
            rowKey={'rowId'}
            style={{
              '--tableMinHeight': `${Math.max(tableY ?? 300, 300)}px`,
            }}
            scroll={{
              x: tableWidth,
              y: Math.max(tableY ?? 200, 200),
            }}
            isBackPagination={true}
            forceColumnsUpdate={true}
            columns={resizableColumns}
            dataSource={reportTableDataSource}
            bordered={true}
            dictionaryData={globalState?.dictData}
            loading={reportLoading || reportDataUpdateLoading}
            // pagination={backPagination}
            onChange={(pagination, filters, sorter, extra) => {
              setReportLoading(true);
              setBackPagination({
                ...backPagination,
                current: 1,
                pageSize: backPagination.pageSize,
              });

              reportDataDetailReq(
                reportBaseInfo?.ReportSettingMasterId,
                reportBaseInfo?.ReportBaseId,
                1,
                backPagination.pageSize,
              );
            }}
            pagination={false}
            components={components}
          />

          <UniPagination
            {...backPagination}
            onTableChange={backTableOnChange}
          />
        </Card>

        <div
          className={`validation-content-container ${
            reportValidateFailureOpen ? 'container-open' : 'container-close'
          }`}
        >
          {/* <div className={'validation-trigger-container'}>
            <span
              className={'label'}
              onClick={() => {
                setReportValidateFailureOpen(!reportValidateFailureOpen);
              }}
            >
              错误报告
            </span>
            <span>（{props?.validationData?.length}条）</span>
          </div> */}

          <div
            className={`validation-table-content ${
              reportValidateFailureOpen ? 'content-open' : 'content-close'
            }`}
          >
            <ValidationResultDetails
              title={reportBaseInfo?.Title}
              validationFailure={validationFailure}
              reportBaseInfo={reportBaseInfo}
              height={tableY}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsReportPersistTable;
