import React, { useContext, useEffect, useRef, useState } from 'react';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import './IcdeMainTable.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { icdeMainColumns } from '../columns/icdeMainColumns';
import { Form, Modal } from 'antd';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import { mainIcdeOutComeOtherPlugin } from '@/pages/dmr/plugins/outcome-other';
import { useModel } from 'umi';
import GridItemContext from '@uni/commons/src/grid-context';

interface IcdeMainTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];
  underConfiguration?: boolean;
  onChange?: (value: any) => void;
}

const icdeCopyKeys =
  (window as any).externalConfig?.['dmr']?.icdeCopyKeys ?? [];

const copyKeys = !isEmptyValues(icdeCopyKeys)
  ? icdeCopyKeys
  : ['IcdeCond', 'IcdeOutcome'];

const icdeOperFirstMain =
  (window as any).externalConfig?.['dmr']?.icdeOperFirstMain ?? false;

const icdeCopyFocusKey =
  (window as any).externalConfig?.['dmr']?.icdeCopyFocusKey ?? undefined;
const icdeDeleteConfirm =
  (window as any).externalConfig?.['dmr']?.icdeDeleteConfirm ?? false;
const icdeMoveMainConfirm =
  (window as any).externalConfig?.['dmr']?.icdeMoveMainConfirm ?? false;

const setFirstItemIsMainIcde = (tableData) => {
  if (tableData?.length > 0) {
    tableData?.forEach((item, index) => {
      if (index === 0) {
        item['IsMain'] = true;
      } else {
        item['IsMain'] = false;
      }
    });
  }
};

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_ICDE_MAIN_ADD, event.target.id);
  },
  COPY: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);
    let index = parseInt(indexString);

    if (index >= 0) {
      Emitter.emit(EventConstant.DMR_ICDE_MAIN_COPY, {
        id: undefined,
        index: index,
      });
    }
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_ICDE_MAIN_DELETE, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisMainTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisMainTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisMainTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisMainTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },
  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('diagnosisMainTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    console.log('DOWN', event);
    Emitter.emit(getArrowUpDownEventKey('diagnosisMainTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};

const clearKeysMap = {
  // 仅用于联动删除使用
  IcdeCode: [
    'IcdeName',
    'IcdeCode',
    'InsurName',
    'InsurCode',
    'HqmsName',
    'HqmsCode',
    'IcdeExtra',
  ],
};

const IcdeMainTable = (props: IcdeMainTableProps) => {
  const {
    globalState: { userInfo },
  } = useModel('@@qiankunStateFromMaster');

  // 获取联动逻辑状态
  const gridContext = useContext(GridItemContext);
  const insurSeparateTableLogic = gridContext?.extra?.insurSeparateTableLogic;

  const itemRef = React.useRef<any>();
  const [form] = Form.useForm();

  const icdeDataSource = Form.useWatch('diagnosisMainTable', props?.form) ?? [];

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    icdeDataSource?.length,
  );

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // 联动逻辑：同步到Insur表格
  const syncToInsurTable = (
    operation: 'add' | 'delete' | 'copy' | 'update' | 'reorder',
    payload: any,
  ) => {
    // 检查权限 只有在联动未被禁用时才执行
    if (!insurSeparateTableLogic?.isTableLinkageDisabled) {
      const currentInsurData =
        props?.form?.getFieldValue('diagnosis-insur-table') || [];
      let newInsurData = [...currentInsurData];

      switch (operation) {
        case 'add':
          const newInsurItem = {
            id: payload.id,
            UniqueId: payload.UniqueId,
            IsReported: true,
            // 可以根据IcdeCode获取对应的医保编码和名称
          };
          newInsurData.push(newInsurItem);
          break;

        case 'delete':
          const deletedUniqueId = payload.UniqueId;
          if (deletedUniqueId) {
            const insurIndex = newInsurData.findIndex(
              (item) => item.UniqueId === deletedUniqueId,
            );
            if (insurIndex >= 0) {
              newInsurData.splice(insurIndex, 1);
            }
          }
          break;

        case 'copy':
          const { originalItem, copiedItem } = payload;
          const originalInsurItem = newInsurData.find(
            (item) => item.UniqueId === originalItem.UniqueId,
          );
          if (originalInsurItem) {
            let copiedInsurItem = {
              IsReported: true,
              id: copiedItem.id,
              UniqueId: copiedItem.UniqueId,
            };
            copyKeys?.forEach((key) => {
              copiedItem[key] = originalInsurItem[key];
            });

            const insurIndex = newInsurData.findIndex(
              (item) => item.UniqueId === originalItem.UniqueId,
            );
            newInsurData.splice(insurIndex + 1, 0, copiedInsurItem);
          }
          break;

        case 'update':
          // onValuesChange 触发的更新操作
          const { changedValues, recordList } = payload;
          // 获取变化的id
          const changedIds = Object.keys(changedValues);

          changedIds.forEach((changedId) => {
            // 在recordList中找到对应的数据
            const changedRecord = recordList.find(
              (record) => record.id?.toString() === changedId?.toString(),
            );
            if (changedRecord && changedRecord.UniqueId) {
              // 在医保数据中找到对应的条目
              const insurIndex = newInsurData.findIndex(
                (item) => item.UniqueId === changedRecord.UniqueId,
              );
              if (insurIndex >= 0) {
                // 更新 InsurCode 和 InsurName
                if (changedRecord.InsurCode !== undefined) {
                  newInsurData[insurIndex].InsurCode = changedRecord.InsurCode;
                }
                if (changedRecord.InsurName !== undefined) {
                  newInsurData[insurIndex].InsurName = changedRecord.InsurName;
                }
              }
            }
          });
          break;

        case 'reorder':
          // onTableDataSourceOrderChange 触发的重新排序操作
          const { tableData } = payload;
          // 根据新的tableData顺序重新排序医保数据
          const reorderedInsurData = [];
          tableData.forEach((mainItem) => {
            if (mainItem.UniqueId) {
              const matchingInsurItem = newInsurData.find(
                (insurItem) => insurItem.UniqueId === mainItem.UniqueId,
              );
              if (matchingInsurItem) {
                reorderedInsurData.push(matchingInsurItem);
              }
            }
          });
          newInsurData = reorderedInsurData;

          break;
      }
      // 更新insur form
      props?.form?.setFieldValue(
        'diagnosis-insur-table',
        cloneDeep(newInsurData),
      );
      console.log(
        'syncToInsurTable',
        operation,
        newInsurData,
        props?.form?.getFieldsValue(),
      );
      triggerFormValueChangeEvent('diagnosis-insur-table');

      // 通知医保表格进行重新渲染
      Emitter.emit(EventConstant.DMR_ICDE_INSUR_TABLE_SYNC, {
        operation,
        payload,
        timestamp: Date.now(),
      });
    }
  };

  // 更新行选择状态的函数
  const updateRowSelectionState = () => {
    const tableData = props?.form?.getFieldValue('diagnosis-main-table') || [];
    const validRows = tableData.filter((row) => row.id !== 'ADD');

    const selectedRows = validRows.filter((row) => {
      const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
      return rowSelectionValue === true;
    });

    const allSelected =
      selectedRows.length === validRows.length && validRows.length > 0;
    const indeterminate =
      selectedRows.length > 0 && selectedRows.length < validRows.length;
    const hasSelection = selectedRows.length > 0;

    Emitter.emit(`DMR_ROW_SELECTION_STATE_UPDATE_diagnosisMainTable`, {
      allSelected: allSelected,
      indeterminate: indeterminate,
    });

    // 通知批量删除按钮状态
    Emitter.emit(`DMR_ROW_SELECTION_BATCH_UPDATE_diagnosisMainTable`, {
      hasSelection: hasSelection,
    });
  };

  useEffect(() => {
    updateRowSelectionState();
    setTableDataSourceSize(icdeDataSource?.length);
  }, [icdeDataSource]);

  // useEffect(() => {
  //   // 当表格的数量改变的时候 重新计算 IsReportedTrueCount
  //   let icdeTableData = props?.form?.getFieldValue('diagnosis-main-table');
  //   let IsReportedTrueCount = 0;
  //   icdeTableData?.forEach((item) => {
  //     if (item?.IsReported === true) {
  //       IsReportedTrueCount++;
  //     }
  //   });
  //   form.setFieldValue('IsReportedTrueCount', IsReportedTrueCount);
  // }, [tableDataSourceSize]);

  const lineUpDownEvents = {
    LINE_UP: (event) => {
      console.log('LINE_UP', event);
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('diagnosisMainTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue(
        'diagnosis-main-table',
      ).length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('diagnosisMainTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  useEffect(() => {
    let columns = mergeColumnsInDmrTable(
      props?.columns,
      icdeMainColumns,
      'IcdeMainTable',
    );

    setTableColumns(columns);
  }, [props?.columns]);

  const removeSwitchOnKeyDown = () => {
    document
      ?.getElementById('diagnosisMainTable')
      ?.querySelectorAll('button[id*=IsReported]')
      ?.forEach((item) => {
        item?.removeEventListener('keydown', () => {});
      });
  };

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocusBySelector(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    // delete事件 好像目前没地方会触发这个
    Emitter.on(getDeletePressEventKey('diagnosisMainTable'), (itemId) => {
      // key 包含 index 和 其他的东西
      console.log('diagnosisMainTable', itemId);
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);

      let clearKeys = [key];
      if (clearKeysMap[key]) {
        clearKeys = clearKeysMap[key];
      }
      clearValuesByKeys(clearKeys, index);

      // 定位到当前这个
      setTimeout(() => {
        document.getElementById(itemId)?.focus();
      }, 100);
    });

    Emitter.on(EventConstant.DMR_ICDE_MAIN_ADD, (focusId?: string) => {
      let rowData = {
        id: Math.round(Date.now() / 1000),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      let tableData = props?.form?.getFieldValue('diagnosis-main-table');

      tableData.splice(tableData.length, 0, rowData);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      props?.form?.setFieldValue('diagnosis-main-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-main-table');

      // 联动逻辑：同步新增到Insur表格
      syncToInsurTable('add', rowData);

      setWaitFocusId(
        `div[id=diagnosisMainTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
      );
      setTableDataSourceSize(tableData?.length);
    });

    // Emitter.on(EventConstant.DMR_ICDE_MAIN_INSURE_MAIN, (data) => {
    //   let index = data?.index;
    //   if (index > -1) {
    //     let tableData = props?.form?.getFieldValue('diagnosis-main-table');

    //     tableData
    //       ?.filter((item) => item?.id !== 'ADD')
    //       .forEach((item, rowDataIndex) => {
    //         let currentFormValue =
    //           form.getFieldsValue()?.[item?.id]?.['IsMain'];
    //         if (index === rowDataIndex) {
    //           item['IsMain'] = currentFormValue;
    //           form.setFieldValue([item?.id, 'IsMain'], currentFormValue);

    //           // 同步勾选 上报
    //           if (currentFormValue === true) {
    //             item['IsReported'] = true;
    //             form.setFieldValue([item?.id, 'IsReported'], true);
    //           }
    //         } else {
    //           item['IsMain'] = false;
    //           form.setFieldValue([item?.id, 'IsMain'], false);
    //         }
    //       });

    //     props?.form?.setFieldValue(
    //       'diagnosis-main-table',
    //       cloneDeep(tableData),
    //     );
    //     triggerFormValueChangeEvent('diagnosis-main-table');

    //     // 更新IsReportedCount
    //     let IsReportedTrueCount = 0;
    //     tableData?.forEach((item) => {
    //       if (item?.IsReported === true) {
    //         IsReportedTrueCount++;
    //       }
    //     });
    //     form.setFieldValue('IsReportedTrueCount', IsReportedTrueCount);
    //   }
    // });

    // Emitter.on(EventConstant.DMR_ICDE_MAIN_REPORT, (checked) => {
    //   let IsReportedTrueCount = form.getFieldValue('IsReportedTrueCount');
    //   if (checked) {
    //     IsReportedTrueCount++;
    //   } else {
    //     IsReportedTrueCount--;
    //   }
    //   form.setFieldValue('IsReportedTrueCount', IsReportedTrueCount);
    // });

    // 处理全选/反选事件
    Emitter.on(`DMR_ROW_SELECTION_SELECT_ALL_diagnosisMainTable`, (data) => {
      const tableData =
        props?.form?.getFieldValue('diagnosis-main-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 批量更新所有行的选中状态
      validRows.forEach((row) => {
        form.setFieldValue([row.id, 'RowSelection'], data.checked);
      });

      // 触发表单值变化事件
      triggerFormValueChangeEvent('diagnosis-main-table');

      // 通知选中状态更新
      updateRowSelectionState();
    });

    // 处理单个行选择变化事件
    Emitter.on(`DMR_ROW_SELECTION_ITEM_CHANGE_diagnosisMainTable`, (data) => {
      // 延迟更新状态，确保表单值已经更新
      setTimeout(() => {
        updateRowSelectionState();
      }, 150);
    });

    // 处理批量删除事件
    Emitter.on(`DMR_BATCH_DELETE_diagnosisMainTable`, () => {
      const tableData =
        props?.form?.getFieldValue('diagnosis-main-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 获取选中的行索引
      const selectedIndexes = [];
      validRows.forEach((row, index) => {
        const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
        if (rowSelectionValue === true) {
          selectedIndexes.push(index);
        }
      });

      if (selectedIndexes.length > 0) {
        // 从后往前删除，避免索引变化问题
        const sortedIndexes = selectedIndexes.sort((a, b) => b - a);
        let newTableData = [...tableData];

        sortedIndexes.forEach((index) => {
          newTableData.splice(index, 1);
        });

        // TODO 设定主诊为第一个
        if (icdeOperFirstMain && newTableData.length > 0) {
          setFirstItemIsMainIcde(newTableData);
        }

        // 更新form
        props?.form?.setFieldValue(
          'diagnosis-main-table',
          cloneDeep(newTableData),
        );
        triggerFormValueChangeEvent('diagnosis-main-table');

        setTableDataSourceSize(newTableData?.length);

        // todo 同步删除到Insur表格

        // 延迟更新选择状态
        setTimeout(() => {
          updateRowSelectionState();
        }, 100);
      }
    });

    Emitter.on(EventConstant.DMR_ICDE_MAIN_DELETE, (index) => {
      if (icdeDeleteConfirm) {
        Modal.confirm({
          title: `确定删除第${index + 1} 条诊断数据？`,
          content: '',
          onOk: () => {
            onIcdeItemDelete(index);
          },
          getContainer: () => document.getElementById('dmr-main-container'),
        });
      } else {
        onIcdeItemDelete(index);
      }
    });

    Emitter.on(EventConstant.DMR_ICDE_MAIN_COPY, (payload) => {
      let tableData = props?.form?.getFieldValue('diagnosis-main-table');
      let currentCopyItem = payload?.['id']
        ? form.getFieldValue(payload?.['id'])
        : tableData?.[payload?.index];
      let index = payload?.index;

      let copiedItem = {
        id: Math.round(Date.now() / 1000),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      copyKeys?.forEach((key) => {
        copiedItem[key] = currentCopyItem[key];
      });

      tableData.splice(index + 1, 0, copiedItem);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      if (!isEmptyValues(icdeCopyFocusKey)) {
        setWaitFocusId(
          `div[id=diagnosisMainTable] tbody > tr:nth-child(${
            index + 2
          }) > td input[id*=${icdeCopyFocusKey}]`,
        );
      } else {
        setWaitFocusId(
          `div[id=diagnosisMainTable] tbody > tr:nth-child(${
            index + 2
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);

      // 更新form
      props?.form?.setFieldValue('diagnosis-main-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-main-table');

      // 联动逻辑：同步复制到Insur表格
      syncToInsurTable('copy', {
        originalItem: currentCopyItem,
        copiedItem: copiedItem,
      });
    });

    Emitter.on(getArrowUpDownEventKey('diagnosisMainTable'), (payload) => {
      let type = payload?.type;
      const icdeDataSource = props?.form?.getFieldValue('diagnosis-main-table');
      console.log('payload', payload);
      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > icdeDataSource?.length - 1) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('diagnosisMainTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();

      Emitter.off(EventConstant.DMR_ICDE_MAIN_ADD);
      Emitter.off(EventConstant.DMR_ICDE_MAIN_DELETE);
      // Emitter.off(EventConstant.DMR_ICDE_MAIN_INSURE_MAIN);
      // Emitter.off(EventConstant.DMR_ICDE_MAIN_REPORT);
      Emitter.off(EventConstant.DMR_ICDE_MAIN_COPY);
      Emitter.off(`DMR_ROW_SELECTION_SELECT_ALL_diagnosisMainTable`);
      Emitter.off(`DMR_ROW_SELECTION_ITEM_CHANGE_diagnosisMainTable`);
      Emitter.off(`DMR_BATCH_DELETE_diagnosisMainTable`);

      Emitter.off(getDeletePressEventKey('diagnosisMainTable'));
      Emitter.off(getArrowUpDownEventKey('diagnosisMainTable'));
    };
  }, []);

  const onIcdeItemDelete = (index: number) => {
    if (index > -1) {
      let tableData = props?.form?.getFieldValue('diagnosis-main-table');
      const deletedItem = tableData[index]; // 保存被删除的项用于联动
      tableData.splice(index, 1);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      // 更新form
      props?.form?.setFieldValue('diagnosis-main-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-main-table');

      // 删除的时候 给出当前那个选中的
      // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
      // 表格中不存在即写第0个的icdeName 建议写死
      let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
      if (dataItems?.length > 0) {
        setWaitFocusId(
          `div[id=diagnosisMainTable] tbody > tr:nth-child(${
            index >= dataItems.length - 1 ? dataItems.length : index + 1
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);

      // 联动逻辑：同步删除到Insur表格
      if (deletedItem) {
        syncToInsurTable('delete', deletedItem);
      }
    }
  };

  const clearValuesByKeys = (keys, index) => {
    const icdeDataSource = props?.form?.getFieldValue('diagnosis-main-table');
    let formItemId = icdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('diagnosis-main-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });

    props?.form?.setFieldValue('diagnosis-main-table', cloneDeep(tableData));
    triggerFormValueChangeEvent('diagnosis-main-table');
  };

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      extraFormItemKeys={['IsReportedTrueCount']}
      formItemContainerClassName={'form-content-item-container'}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'diagnosisMainTable'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{
        x: 'max-content',
      }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      dataSource={(props?.form?.getFieldValue('diagnosis-main-table') ?? [])
        ?.filter((item) => item?.id !== 'ADD')
        ?.map((item) => {
          if (!item['id']) {
            item['id'] = Math.round(Date.now() / 1000);
          }
          return item;
        })
        ?.concat({
          id: 'ADD',
        })}
      rowKey={'id'}
      onDragEndPre={(active, over) => {
        return new Promise((resolve, reject) => {
          if (icdeMoveMainConfirm === false) {
            resolve(true);
          } else {
            if (active.newIndex === 0) {
              const tableData = props?.form?.getFieldValue(
                'diagnosis-main-table',
              );
              let activeItem = tableData?.at(active.oldIndex);
              Modal.confirm({
                title: `确定移动${
                  isEmptyValues(activeItem?.IcdeCode)
                    ? '此条数据'
                    : `诊断编码: ${activeItem.IcdeCode}`
                }到主诊？`,
                content: '',
                okText: '确定',
                cancelText: '取消',
                onOk: () => {
                  resolve(true);
                },
                onCancel: () => {
                  resolve(false);
                },
                getContainer: () =>
                  document.getElementById('dmr-main-container'),
              });
            } else {
              resolve(true);
            }
          }
        });
      }}
      onDragExtra={(tableData) => {
        if (icdeOperFirstMain) {
          setFirstItemIsMainIcde(tableData);
        }
      }}
      onValuesChange={(recordList, changedValues) => {
        props?.form?.setFieldValue('diagnosis-main-table', recordList);
        triggerFormValueChangeEvent('diagnosis-main-table');

        // 触发联动更新
        if (changedValues && Object.keys(changedValues).length > 0) {
          syncToInsurTable('update', {
            changedValues,
            recordList,
          });
        }
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        props?.form?.setFieldValue(
          'diagnosis-main-table',
          cloneDeep(tableData),
        );

        // 触发联动重新排序
        syncToInsurTable('reorder', {
          tableData,
          dataOrder,
        });

        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent('diagnosis-main-table');
      }}
      columns={tableColumns}
      enableRowSelection={true}
    />
  );
};

export default React.memo(IcdeMainTable);
