import {
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormSwitch,
  StepsForm,
  ProFormDependency,
  ProFormSelect,
  ProFormDigit,
} from '@uni/components/src/pro-form';
import _ from 'lodash';
import { Divider, Form, Input, Select, Space, Spin, Typography } from 'antd';

const MenuFormItems = ({ editValueObj, dictData }) => {
  return (
    <ProForm.Group
      title="菜单配置"
      colProps={{
        span: 24,
      }}
    >
      <ProFormSwitch
        name="NeedMenu"
        label="菜单配置"
        initialValue={
          !!editValueObj?.Master?.MenuItemUrl ||
          !!editValueObj?.Master?.Slug ||
          !!editValueObj?.Master?.MenuDirectories?.length ||
          !!editValueObj?.Master?.MenuSort ||
          false
        }
        colProps={{
          span: 2,
        }}
      />
      <ProFormDependency name={['NeedMenu']}>
        {({ NeedMenu }) => {
          if (NeedMenu) {
            return (
              <>
                <ProFormText
                  name={['MasterArgs', 'MenuItemUrl']}
                  label="菜单Url"
                  width="sm"
                  initialValue={editValueObj?.Master?.MenuItemUrl || undefined}
                  placeholder="请输入菜单Url"
                  colProps={{
                    span: 4,
                  }}
                />
                <ProFormText
                  name={['MasterArgs', 'Slug']}
                  label="菜单Slug"
                  width="sm"
                  initialValue={editValueObj?.Master?.Slug || undefined}
                  placeholder="请输入菜单Slug"
                  colProps={{
                    span: 4,
                  }}
                />
                <ProFormSelect
                  name={['MasterArgs', 'MenuDirectories']}
                  label="菜单目录"
                  initialValue={
                    editValueObj?.Master?.MenuDirectories || undefined
                  }
                  placeholder="请选择菜单目录"
                  // width="sm"
                  mode="tags"
                  options={dictData.MenuDirectories}
                  colProps={{
                    span: 6,
                  }}
                />
                <ProFormDigit
                  name={['MasterArgs', 'MenuSort']}
                  label="菜单顺序"
                  width="sm"
                  initialValue={editValueObj?.Master?.MenuSort || undefined}
                  fieldProps={{ precision: 0 }}
                  placeholder="请输入菜单顺序"
                  colProps={{
                    span: 4,
                  }}
                />
              </>
            );
          }
        }}
      </ProFormDependency>
    </ProForm.Group>
  );
};

export default MenuFormItems;
