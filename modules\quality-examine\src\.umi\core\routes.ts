// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/reviewer"
  },
  {
    "path": "/reviewer",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__review__reviewer__index' */'@/pages/review/reviewer/index'), loading: LoadingComponent})
  },
  {
    "path": "/auditee",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__review__auditee__index' */'@/pages/review/auditee/index'), loading: LoadingComponent})
  },
  {
    "path": "/management",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__review__management__index' */'@/pages/review/management/index'), loading: LoadingComponent})
  },
  {
    "path": "/analysis",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__review__analysis__index' */'@/pages/review/analysis/index'), loading: LoadingComponent})
  },
  {
    "path": "/report",
    "exact": false,
    "microApp": "report",
    "microAppProps": {
      "autoSetLoading": true
    },
    "component": (() => {
          const { getMicroAppRouteComponent } = umiExports;
          return getMicroAppRouteComponent({ appName: 'report', base: 'quality-examine', masterHistoryType: 'browser', routeProps: {'settings':{},'autoSetLoading':true} })
        })()
  },
  {
    "path": "/highlight/:id",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__review__highlightReport__index' */'@/pages/review/highlightReport/index'), loading: LoadingComponent})
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
