import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { UniTable } from '@uni/components/src';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { ColumnItem, RespVO } from '@uni/commons/src/interfaces';
import {
  ReportMasterItem,
  ReportBaseInfo,
  ReportDetailItem,
  ReportItem,
  TableBaseProps,
  ReportRelationsItem,
  ReportDependenciesItem,
} from '@/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { ReportEventConstant } from '@/constants';
import {
  Button,
  Card,
  message,
  Space,
  notification,
  TableProps,
  Divider,
} from 'antd';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  DETAIL,
  EXPORT,
  REFRESH,
  DETAIL_READONLY_EXPORT,
} from '@/tables/constants';
import { loadReport, reloadReport } from '@/tables/network';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { downloadFile } from '@uni/utils/src/download';
import { useModel } from 'umi';
import {
  masterItemExtraConfig,
  reportTableGroupNameHeaderProcessor,
  tableCustomTitleProcessor,
  transformReportColumnsWithDataTypeIntOrderable,
} from '@/utils';
import { useAntdResizableHeader } from '@uni/components/src/table/resizable-column/header';
import cloneDeep from 'lodash/cloneDeep';
import isEmpty from 'lodash/isEmpty';
import { exportExcel } from '@uni/utils/src/excel-export';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import { commonBusinessDomain } from '@uni/services/src/commonService';
import UniPagination from '@uni/components/src/table/pagination';
import { isEmptyValues } from '@uni/utils/src/utils';
import PivotTransformService, { hasPivotColumn } from '@/tables/pivot';
import { enhanceButtonEvent } from '@/utils/smartEmitter';

const messageKey = 'exporting';

interface DetailsReportReadonlyTableProps extends TableBaseProps {
  // reportDependencyItem?: ReportDependenciesItem;
  // reportRelations?: ReportRelationsItem[];
}

const pivotServiceInstance = new PivotTransformService();

const DetailsReportReadonlyTable = (props: DetailsReportReadonlyTableProps) => {
  const [reportBaseInfo, setReportBaseInfo] =
    useState<ReportBaseInfo>(undefined);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [reportTableDataSource, setReportTableDataSource] = useState([]);
  const [reportTableColumns, setReportTableColumns] = useState([]);

  const [reportTableExtraColumns, setReportTableExtraColumns] = useState([]);

  const [masterItem, setMasterItem] = useState<ReportMasterItem>(
    props?.masterItem,
  );

  const [reportDataQueryLoading, setReportDataQueryLoading] = useState(false);

  const [tableCardTitle, setTableCardTitle] = useState<string>('');

  const [reportQueryArgs, setReportQueryArgs] = useState({});

  const { components, resizableColumns, tableWidth, resetColumns } =
    useAntdResizableHeader({
      columns: React.useMemo(() => {
        return reportTableColumns;
      }, [reportTableColumns]),
    });

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      clearData: () => {
        setReportTableDataSource([]);
      },
      instantQuery: (masterId: string, args: any) => {
        // 实时查询
        let currentPagination = Object.assign({}, backPagination);
        currentPagination.current = 1;
        currentPagination.pageSize = 30;

        reportColumnsReq(masterId);
        reportDataDetailReq(
          masterId,
          args,
          currentPagination?.current,
          currentPagination?.pageSize,
        );
        setBackPagination({
          current: 1,
          pageSize: 30,
          total: 0,
          pageSizeOptions: ['10', '20', '30', '50'],
        });
      },
    };
  });

  /**
   * 后端分页 start
   */
  const [backPagination, setBackPagination] = useState({
    current: 1,
    pageSize: 30,
    total: 0,
    pageSizeOptions: ['10', '20', '30', '50'],
  });

  // 后端分页OnChange
  const backTableOnChange: TableProps<any>['onChange'] = (pagination) => {
    setBackPagination({
      ...backPagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });

    reportDataDetailReq(
      masterItem?.Id,
      reportQueryArgs,
      pagination.current,
      pagination.pageSize,
    );
  };
  /**
   * 后端分页 end
   */

  const [operationBtns, setOperationBtns] = useState([]);

  useEffect(() => {
    // 停止轮询
    stopPolling();
    let masterItem = cloneDeep(props?.masterItem);

    // reset
    resetState();
    // 解析ExtraConfig
    let extraConfigs = masterItemExtraConfig(masterItem?.ExtraConfig);
    if (!isEmptyValues(extraConfigs?.extraColumns)) {
      setReportTableExtraColumns(extraConfigs?.extraColumns ?? []);
    }

    setMasterItem(masterItem);
    setTableCardTitle(tableCustomTitleProcessor(masterItem, ''));
  }, [props?.masterItem]);

  // 加载数据
  useEffect(() => {
    Emitter.on(
      `${ReportEventConstant.REPORT_ITEM_QUERY_SUBMIT}_${props?.masterItem?.Id}`,
      (data) => {
        reportColumnsReq(masterItem?.Id);
        setReportQueryArgs(data?.args);

        reportDataDetailReq(
          masterItem?.Id,
          data?.args,
          backPagination.current,
          backPagination.pageSize,
        );

        // 根据推来的数据 构建title
        setTableCardTitle(
          tableCustomTitleProcessor(masterItem, data?.selectedItem),
        );
      },
    );

    Emitter.on(
      `${ReportEventConstant.REPORT_GROUP_DATA_CLEAR}_${props?.masterItem?.Id}`,
      () => {
        setReportTableDataSource([]);
      },
    );

    return () => {
      Emitter.off(
        `${ReportEventConstant.REPORT_ITEM_QUERY_SUBMIT}_${props?.masterItem?.Id}`,
      );
      Emitter.off(
        `${ReportEventConstant.REPORT_GROUP_DATA_CLEAR}_${props?.masterItem?.Id}`,
      );
    };
  }, [masterItem, reportTableExtraColumns]);

  // refresh load archive
  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_REFRESH, async () => {
      let currentPagination = Object.assign({}, backPagination);
      currentPagination.current = 1;
      currentPagination.pageSize = 30;

      reportDataDetailReq(
        masterItem?.Id,
        reportQueryArgs,
        currentPagination?.current,
        currentPagination?.pageSize,
      );
      setBackPagination({
        current: 1,
        pageSize: 30,
        total: 0,
        pageSizeOptions: ['10', '20', '30', '50'],
      });
    });

    // Emitter.on(ReportEventConstant.REPORT_LOAD, () => {
    //   loadReport(reportItem?.ReportSettingMasterId, reportItem?.ReportBaseId);
    //   // TODO 启动轮询
    //   reportBaseInfoDataLoadReq(
    //     reportItem?.ReportSettingMasterId,
    //     reportItem?.ReportBaseId,
    //   );
    // });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_REFRESH);
      // Emitter.off(ReportEventConstant.REPORT_LOAD);
    };
  }, [masterItem, reportQueryArgs]);

  // 导出结果
  useEffect(() => {
    Emitter.on(
      `${ReportEventConstant.REPORT_EXPORT}_${masterItem?.Id}`,
      async () => {
        message.success(
          `报表： ${tableCardTitle ?? masterItem?.Title}导出中，请稍后`,
        );

        exportStart({
          ReportSettingMasterId: masterItem?.Id,
          ReportArgs: reportQueryArgs,
        });
      },
    );

    return () => {
      Emitter.off(`${ReportEventConstant.REPORT_EXPORT}_${masterItem?.Id}`);
    };
  }, [masterItem, reportQueryArgs]);

  // 导出
  const { run: exportStart } = useRequest(
    (data) => {
      return uniCommonService('Api/Report/Report/ExportQueryDetails', {
        method: 'POST',
        data: {
          // 默认
          DtParam: {
            Draw: 1,
            Start: 0,
            Length: 2147483647,
          },
          ...data,
        },
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => res,
      onSuccess(res, params) {
        console.log(res);
        if (res.response?.status === 202) {
          exportCheck(res.data.Id);
          message.loading({
            content: '正在导出...',
            key: messageKey,
            duration: 0,
          });
        } else {
        }
      },
    },
  );
  // 轮询 export check
  const { run: exportCheck, cancel: cancelExportCheck } = useRequest(
    (id) => {
      return uniCommonService('Api/Common/ExportCenter/GetExportRecord', {
        method: 'POST',
        params: { Id: id },
      });
    },
    {
      manual: true,
      pollingInterval: 1000,
      pollingWhenHidden: true,
      //   formatResult: (res: RespVO<any>) => res,
      onSuccess(res, params) {
        if (res?.Status === '100') {
          cancelExportCheck();

          message.destroy(messageKey);
          message.success('导出成功');

          let a = document.createElement('a');
          a.target = '_blank';
          a.href = `${commonBusinessDomain}/Api/Common/Blob/Download?Id=${res.BlobId}`;
          a.download = `${tableCardTitle ?? masterItem?.Title}-${dayjs().format(
            'YYYYMMDD_HHmmss',
          )}`;
          //   document.body.appendChild(a);
          a.click();
          //   document.body.removeChild(a);
        } else {
        }
      },
    },
  );

  useEffect(() => {
    processOperationBtns();
  }, [reportBaseInfo, masterItem, reportQueryArgs]);

  const resetState = () => {
    //reset
    setReportTableDataSource([]);
    setReportTableColumns([]);
    setBackPagination({
      current: 1,
      pageSize: 30,
      total: 0,
      pageSizeOptions: ['10', '20', '30', '50'],
    });
  };

  const processOperationBtns = () => {
    let btns = [];

    if (masterItem?.Id) {
      btns.push(DETAIL());
      btns.push(<Divider type="vertical" />);
      if (masterItem?.EnableExport === true) {
        const originalExport = EXPORT(reportTableDataSource?.length === 0);
        // 使用enhanceButtonEvent增强按钮，传入事件名称和表格ID
        const enhancedExport = enhanceButtonEvent(
          originalExport,
          ReportEventConstant.REPORT_EXPORT,
          masterItem?.Id,
        );
        btns.push(enhancedExport);
      }
    }

    return btns;
  };

  const stopPolling = () => {
    setReportDataQueryLoading(false);
  };

  // columns
  const { loading: reportColumnsLoading, run: reportColumnsReq } = useRequest(
    (id) => {
      return uniCommonService('Api/Report/Report/GetReportDataTablesColumns', {
        params: {
          ReportSettingMasterId: id,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let transformedTableColumns = reportTableExtraColumns.concat(
            reportTableGroupNameHeaderProcessor(
              tableColumnBaseProcessor(
                [],
                transformReportColumnsWithDataTypeIntOrderable(
                  masterItem?.ReportMode,
                  response?.data,
                ),
              ),
            ),
          );

          if (hasPivotColumn(transformedTableColumns)) {
            pivotServiceInstance.setTableColumns(transformedTableColumns);
            setReportTableColumns([]);
          } else {
            setReportTableColumns(transformedTableColumns);
          }
        } else {
          setReportTableColumns([]);
        }
      },
    },
  );

  const { loading: reportDataDetailLoading, run: reportDataDetailReq } =
    useRequest(
      (masterId, reportArgs, current, pageSize) => {
        let data = {
          ReportSettingMasterId: masterId,
          ReportArgs: reportArgs,
        };

        data['DtParam'] = {
          Start: (current - 1) * pageSize,
          Length: pageSize,
        };

        return uniCommonService('Api/Report/Report/QueryDetails', {
          method: 'POST',
          data: data,
          requestType: 'json',
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<ReportDetailItem>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            // TODO validate

            if (pivotServiceInstance?.hasPivotColumns === true) {
              let pivotData = pivotServiceInstance.pivotColumnsDataTransformer(
                response?.data?.data,
                [],
              );

              setReportTableColumns(pivotData?.columns);
              setReportTableDataSource(pivotData?.dataSources ?? []);
            } else {
              setReportTableDataSource(
                response?.data?.data?.map((item) => {
                  return {
                    rowId: uuidv4(),
                    ...item,
                  };
                }),
              );
            }

            setBackPagination({
              ...backPagination,
              total: response?.data?.recordsTotal || 0,
            });
          } else {
            setReportTableDataSource([]);
          }
        },
      },
    );

  const tableY =
    document.getElementById('report-content-container')?.offsetHeight -
    (document.getElementsByClassName('ant-table-thead')?.[0]?.clientHeight ||
      0) -
    (document.getElementById('query-header-container')?.offsetHeight || 0) -
    64 -
    64 -
    20 -
    (document.getElementById('report-title')?.offsetHeight || 0) -
    16 -
    4 -
    23;

  return (
    <div
      id={'detail-table-readonly-container'}
      className={'detail-table-readonly-container'}
    >
      <Card
        title={tableCardTitle ?? masterItem?.Title}
        id={'report-content-container'}
        className={'report-content-container'}
        extra={
          <>
            <Space>{processOperationBtns()}</Space>
          </>
        }
      >
        <UniTable
          id={'report-table'}
          rowKey={'rowId'}
          scroll={{
            x: 'max-content',
            y: Math.max(tableY ?? 200, 200),
          }}
          widthDetectAfterDictionary
          forceColumnsUpdate={true}
          columns={reportTableColumns} // resizableColumns
          dataSource={reportTableDataSource}
          clickable={false}
          bordered={true}
          loading={reportDataDetailLoading || reportDataQueryLoading}
          // pagination={backPagination}
          // onChange={backTableOnChange}
          pagination={false}
          isBackendPagination={true}
          onChange={(pagination, filters, sorter, extra) => {
            setBackPagination({
              ...backPagination,
              current: 1,
              pageSize: backPagination.pageSize,
            });

            reportDataDetailReq(
              masterItem?.Id,
              reportQueryArgs,
              1,
              backPagination.pageSize,
            );
          }}
          dictionaryData={globalState?.dictData}
          components={components}
        />

        <UniPagination {...backPagination} onTableChange={backTableOnChange} />
      </Card>
    </div>
  );
};

export default DetailsReportReadonlyTable;
