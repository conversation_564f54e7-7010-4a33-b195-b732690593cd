import { HierarchyItem, RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import uniq from 'lodash/uniq';
import Constants from '@/constants';
import { EventConstant } from '@uni/utils/src/emitter';
import { isEmpty } from 'lodash';
import { processProvinceCityDistrictData } from '@/layouts/root/processor';
import merge from 'lodash/merge';
import { isEmptyValues } from '@uni/utils/src/utils';

export const dictModuleInitialize = async (dispatch: any) => {
  // 先行获取数据库中保存的 DmrModules
  // 这个是 首页配置保存的字段 module请求前需要unique
  let savedDmrModulesResponse: RespVO<any> = await uniCommonService(
    'Api/Sys/ClientKitSys/GetValues',
    {
      method: 'POST',
      requestType: 'json',
      data: {
        IdentityCode: 'Global',
        IdentityType: 'Global',
        configModules: ['DmrModules'],
      },
    },
  );

  let savedDmrModules = savedDmrModulesResponse?.data?.DmrModules ?? {};

  let dictionaryResponses = await Promise.all([
    dispatch({
      type: 'uniDict/fetchDictionaryData',
      param: {
        modules: uniq([
          ...Constants.modules,
          ...(savedDmrModules?.['Default'] ?? []),
        ]),
        moduleGroup: '',
        noSave: true,
      },
    }),

    dispatch({
      type: 'uniDict/fetchDictionaryData',
      param: {
        modules: uniq([
          ...Constants.mrModules,
          ...(savedDmrModules?.['Mr'] ?? []),
        ]),
        moduleGroup: 'Mr',
        noSave: true,
      },
    }),

    dispatch({
      type: 'uniDict/fetchDictionaryData',
      param: {
        modules: uniq([
          ...Constants.dmrModules,
          ...(savedDmrModules?.['Dmr'] ?? []),
        ]),
        moduleGroup: 'Dmr',
        noSave: true,
      },
    }),

    dispatch({
      type: 'uniDict/fetchDictionaryData',
      param: {
        modules: uniq([
          ...Constants.insurModules,
          ...(savedDmrModules?.['Insur'] ?? []),

          // 追加 dmr系列 modules
          ...(savedDmrModules?.['Dmr'] ?? []),
          ...Constants.dmrModules,
        ]),
        moduleGroup: 'Insur',
        noSave: true,
      },
    }),
  ]);

  let resultDictData = {};

  dictionaryResponses?.forEach((modulesData, index) => {
    if (!isEmptyValues(modulesData)) {
      if (index === 0) {
        // 表示是 no group data
        // 设定默认的从dictData中来的数据
        (global?.window as any)?.eventEmitter?.emit(
          EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
          {
            hospCodes: [modulesData?.dictData?.['Hospital']?.at(0)?.Code],
            hospCode: `${modulesData?.dictData?.['Hospital']?.at(0)?.Code}`,
            initial: true,
          },
        );
      }

      if (index === 2) {
        if (
          !isEmpty(modulesData?.dictData?.['Dmr']?.Prov) &&
          !isEmpty(modulesData?.dictData?.['Dmr']?.City) &&
          !isEmpty(modulesData?.dictData?.['Dmr']?.Coty)
        ) {
          let provinceCityDistrictData = processProvinceCityDistrictData(
            modulesData?.dictData?.['Dmr']?.Prov,
            modulesData?.dictData?.['Dmr']?.City,
            modulesData?.dictData?.['Dmr']?.Coty,
            modulesData?.dictData?.['Dmr']?.Subd ?? [],
          );

          // console.log('provinceCityDistrictData', provinceCityDistrictData);

          Object.keys(provinceCityDistrictData)?.forEach((key) => {
            modulesData['dictData']['Dmr'][key] = provinceCityDistrictData[key];
          });
        }
      }

      if (index === 3) {
        if (
          !isEmpty(modulesData?.dictData?.['Insur']?.Prov) &&
          !isEmpty(modulesData?.dictData?.['Insur']?.City) &&
          !isEmpty(modulesData?.dictData?.['Insur']?.Coty)
        ) {
          let provinceCityDistrictData = processProvinceCityDistrictData(
            modulesData?.dictData?.['Insur']?.Prov,
            modulesData?.dictData?.['Insur']?.City,
            modulesData?.dictData?.['Insur']?.Coty,
            modulesData?.dictData?.['Insur']?.Subd ?? [],
          );

          Object.keys(provinceCityDistrictData)?.forEach((key) => {
            modulesData['dictData']['Insur'][key] =
              provinceCityDistrictData[key];
          });
        }
      }

      merge(resultDictData, modulesData ?? {});
    }
  });

  dispatch({
    type: 'uniDict/saveDictionaryData',
    data: resultDictData,
  });
};
